<!-- 按巡检作业查询组件 -->
<template>
    <div>
        <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
            <div v-show="showSearch" class="mb-[10px]">
                <el-card shadow="hover">
                    <el-form ref="queryFormRef" :model="queryParams" :inline="true">
                        <el-form-item>
                            <!-- 作业单日期 -->
                            <!-- <el-date-picker
                                v-model="queryParams.params.taskDate"
                                clearable
                                type="datetimerange"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            /> -->

                            <el-date-picker
                                v-model="dateRangeTaskDate"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                type="daterange"
                                range-separator="-"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                                clearable
                            />
                        </el-form-item>
                        <el-form-item prop="name">
                            <el-input v-model="queryParams.name" placeholder="请输入作业单名称" clearable @keyup.enter="handleQuery" />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                        </el-form-item>
                    </el-form>
                </el-card>
            </div>
        </transition>
        <el-card shadow="never">
            <el-table v-loading="loading" :data="taskList" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="" align="center" prop="id" v-if="false" />
                <el-table-column label="作业单名称" align="center" prop="name" />
                <el-table-column label="计划作业日期" align="center" prop="taskDate">
                    <template #default="scope">
                        {{ formatDate(scope.row.taskDate) }}
                    </template>
                </el-table-column>
                <el-table-column label="作业类型" align="center"> 年度巡检 </el-table-column>
                <el-table-column label="班次" align="center" prop="shiftType">
                    <template #default="scope">
                        <dict-tag :options="work_order_white_evening" :value="scope.row.shiftType" />
                    </template>
                </el-table-column>
                <!-- <el-table-column label="巡检班组" align="center" prop="teamId" >

								</el-table-column> -->
                <el-table-column label="巡检路线" align="center" prop="inspectionLineNameLabel">
                    <template #default="scope">
                        <span v-if="scope.row.inspectionLineNameLabel">
                            {{ scope.row.inspectionLineNameLabel }}
                        </span>
                        <span v-else>-</span>
                    </template>
                </el-table-column>
                <el-table-column label="实际开工日期" align="center" prop="taskStartDate">
                    <template #default="scope">
                        {{ formatDate(scope.row.taskStartDate) }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="120">
                    <template #default="scope">
                        <el-button type="primary" :disabled="scope.row.currentStatus == 'START'" link @click="handleInspectDevice(scope.row)">
                            巡检设备
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <pagination
                v-show="total > 0"
                :total="total"
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                @pagination="getList"
            />
        </el-card>
    </div>
</template>

<script setup lang="ts">
import { listInspectionTasks, getTask } from '@/api/subProject/plan/task'
import { InspectionTaskVO, TaskForm, TaskQuery } from '@/api/plan/task/types'
import { useAppStore } from '@/store/modules/app'
import { useRouter } from 'vue-router'
import { ref, reactive, toRefs, onMounted, onBeforeUnmount, watch, getCurrentInstance, ComponentInternalInstance, nextTick } from 'vue'

const appStore = useAppStore()
const router = useRouter()
const { proxy } = getCurrentInstance() as ComponentInternalInstance

const taskList = ref<InspectionTaskVO[]>([])
const loading = ref(true)
const showSearch = ref(true)
const ids = ref<Array<string | number>>([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)

const queryFormRef = ref<ElFormInstance>()

const initFormData: TaskForm = {
    id: undefined,
    projectId: undefined,
    parentId: undefined,
    name: undefined,
    taskType: undefined,
    defineId: undefined,
    weekTaskId: undefined,
    year: undefined,
    month: undefined,
    week: undefined,
    shiftType: undefined,
    taskDate: undefined,
    taskStep: undefined,
    bgnDate: undefined,
    endDate: undefined,
    taskStartDate: undefined,
    taskFinishDate: undefined,
    teamId: undefined,
    sort: undefined,
    publishState: undefined,
    status: undefined,
    currentStatus: undefined,
    tempTask: 'no',
    tempResourceId: ''
}

const data = reactive<PageData<TaskForm, TaskQuery>>({
    form: { ...initFormData },
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectId: undefined,
        parentId: undefined,
        name: undefined,
        taskType: 'inspect',
        defineId: undefined,
        weekTaskId: undefined,
        year: undefined,
        month: undefined,
        week: undefined,
        shiftType: undefined,
        taskDate: undefined,
        taskStep: 'task',
        bgnDate: undefined,
        endDate: undefined,
        teamId: undefined,
        sort: undefined,
        publishState: undefined,
        currentStatus: 'END',
        tempTask: 'no',
        tempResourceId: '',
        params: {}
    },
    rules: {
        id: [{ required: true, message: '不能为空', trigger: 'blur' }]
    }
})

const { queryParams, form, rules } = toRefs(data)

const dateRangeTaskDate = ref<[string, string]>(['', ''])

// 状态缓存相关
const CACHE_KEY = 'inspection_task_search_state'

const { maintenance_strategy, work_order_white_evening } = toRefs<any>(proxy?.useDict('maintenance_strategy', 'work_order_white_evening'))
/** 查询任务列表 */
const getList = async () => {
    loading.value = true

    // 确保项目ID已设置
    if (!queryParams.value.projectId) {
        queryParams.value.projectId = appStore.projectContext.selectedProjectId
    }

    // 设置项目ID
    queryParams.value.projectId = appStore.projectContext.selectedProjectId
    queryParams.value.currentStatus = 'END'
    // 使用独立的日期范围变量进行日期范围查询
    proxy?.addDateRange(queryParams.value, dateRangeTaskDate.value, 'BgnDate')

    const res = await listInspectionTasks(queryParams.value)
    taskList.value = res.rows
    total.value = res.total

    loading.value = false
}

// 缓存搜索状态
const saveSearchState = () => {
    try {
        const state = {
            queryParams: {
                name: queryParams.value.name
            },
            dateRangeTaskDate: dateRangeTaskDate.value,
            showSearch: showSearch.value
        }
        sessionStorage.setItem(CACHE_KEY, JSON.stringify(state))
        console.log('保存巡检任务搜索状态:', state)
    } catch (error) {
        console.error('保存巡检任务搜索状态失败:', error)
    }
}

// 恢复搜索状态
const restoreSearchState = () => {
    try {
        const cached = sessionStorage.getItem(CACHE_KEY)
        if (cached) {
            const state = JSON.parse(cached)

            // 恢复查询参数
            if (state.queryParams) {
                queryParams.value.name = state.queryParams.name
            }

            // 恢复日期范围
            if (state.dateRangeTaskDate) {
                dateRangeTaskDate.value = state.dateRangeTaskDate
            }

            // 恢复搜索框显示状态
            if (state.showSearch !== undefined) {
                showSearch.value = state.showSearch
            }

            console.log('恢复巡检任务搜索状态:', state)
        }
    } catch (error) {
        console.error('恢复巡检任务搜索状态失败:', error)
    }
}

// 清除缓存状态
const clearSearchState = () => {
    sessionStorage.removeItem(CACHE_KEY)
}

/** 搜索按钮操作 */
const handleQuery = () => {
    queryParams.value.pageNum = 1
    queryParams.value.projectId = appStore.projectContext.selectedProjectId
    getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
    // 清空日期范围
    dateRangeTaskDate.value = ['', '']
    queryFormRef.value?.resetFields()

    // 清除缓存状态
    clearSearchState()

    handleQuery()
}
// 格式化日期
const formatDate = (date: string) => {
    if (!date) return '-'
    return date
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: InspectionTaskVO[]) => {
    ids.value = selection.map((item) => item.id)
    single.value = selection.length != 1
    multiple.value = !selection.length
}

/** 新增按钮操作 */
const handleAdd = () => {
    // 移除新增功能
}

/** 导航到查看、指派页面 */
const handleAssign = (operationName: string) => {
    // reset();
    // dialog.visible = true;
    // dialog.title = '添加年度计划目录';
    // proxy?.$router.push('assign');
}

/** 审批按钮操作 */
const handleConfirm = (operationName: string) => {
    // 移除审批功能
}

/** 修改按钮操作 */
const handleUpdate = async (row?: InspectionTaskVO) => {
    // 移除修改功能
}

/** 提交按钮 */
const submitForm = () => {
    // 移除提交功能
}

/** 删除按钮操作 */
const handleDelete = async (row?: InspectionTaskVO) => {
    // 移除删除功能
}

/** 巡检设备按钮操作 */
const handleInspectDevice = (row: InspectionTaskVO) => {
    console.log('巡检设备，任务信息:', row)
    // 跳转到巡检设备详情页面
    router.push({
        name: 'multiRouteDetail',
        query: {
            taskId: row.id
        }
    })
}

/** 导出按钮操作 */
const handleExport = () => {
    proxy?.download(
        'plan/task/export',
        {
            ...queryParams
        },
        `task_${new Date().getTime()}.xlsx`
    )
}

onMounted(() => {
    queryParams.value.projectId = appStore.projectContext.selectedProjectId

    // 恢复搜索状态
    restoreSearchState()

    getList()
})

// 组件卸载前保存状态
onBeforeUnmount(() => {
    saveSearchState()
})

// 监听搜索条件变化，实时保存状态
watch(
    [() => queryParams.value.name, dateRangeTaskDate, showSearch],
    () => {
        // 延迟保存，避免频繁操作
        nextTick(() => {
            saveSearchState()
        })
    },
    { deep: true }
)
</script>

<style lang="scss" scoped>
.loading-text {
    color: #909399;
    font-size: 12px;
}

.error-text {
    color: #f56c6c;
    font-size: 12px;
}

.inspection-lines {
    color: #606266;
    font-size: 12px;
    word-break: break-all;
    line-height: 1.4;
}

.btn-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .filter {
        flex: 1;
    }
    .export {
        margin-left: auto;
    }
}
</style>
