import request from '@/utils/request'
import { AxiosPromise } from 'axios'
import { InspectionByDeviceViewVO, InspectionByDeviceViewQuery } from '@/api/subProject/inspection/inspectionByDeviceView/types'

// 定义TableDataInfo类型
interface TableDataInfo<T> {
    code: number
    msg: string
    rows: T[]
    total: number
}

/**
 * 查询设施即设备信息列表
 * @param query
 * @returns {*}
 */

export const listDevice = (query?: InspectionByDeviceViewQuery): AxiosPromise<InspectionByDeviceViewVO[]> => {
    return request({
        url: '/basic/device/listbyinspection',
        method: 'get',
        params: query
    })
}
