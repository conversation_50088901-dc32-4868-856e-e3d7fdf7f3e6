<!-- 车流组件 -->
<template>
    <div class="traffic-data-container">
        <!-- 页面头部 -->
        <div class="header-section">
            <div class="title-area">
                <h2>小时车流量（进/出隧道）</h2>
                <el-tag type="info" size="small">过去24小时数据</el-tag>
                <el-tag v-if="processedTunnelName" type="success" size="small">{{ processedTunnelName }}</el-tag>
            </div>

            <!-- 查询控件（暂时禁用） -->
            <div class="query-section">
                <el-form :inline="true" class="demo-form-inline">
                    <!--<el-form-item label="时间范围">
                         <el-date-picker
                            v-model="dateRange"
                            type="datetimerange"
                            range-separator="至"
                            start-placeholder="开始时间"
                            end-placeholder="结束时间"
                            disabled
                            style="opacity: 0.5"
                        />
                    </el-form-item>-->
                    <el-form-item>
                        <!-- <el-button type="primary" disabled style="opacity: 0.5">
                            <el-icon><Search /></el-icon>
                            查询（开发中）
                        </el-button> -->
                        <el-button @click="handleRefresh">
                            <el-icon><Refresh /></el-icon>
                            刷新
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-cards" v-if="!loading && statsData">
            <el-row :gutter="20">
                <el-col :span="8">
                    <el-card shadow="hover">
                        <div class="stat-item">
                            <div class="stat-value">{{ statsData.totalTunnels }}</div>
                            <div class="stat-label">隧道总数</div>
                        </div>
                    </el-card>
                </el-col>
                <el-col :span="8">
                    <el-card shadow="hover">
                        <div class="stat-item">
                            <div class="stat-value">{{ formatNumber(statsData.totalInTraffic) }}</div>
                            <div class="stat-label">24小时进隧道总流量</div>
                        </div>
                    </el-card>
                </el-col>
                <el-col :span="8">
                    <el-card shadow="hover">
                        <div class="stat-item">
                            <div class="stat-value">{{ formatNumber(statsData.totalOutTraffic) }}</div>
                            <div class="stat-label">24小时出隧道总流量</div>
                        </div>
                    </el-card>
                </el-col>
                <!-- <el-col :span="6">
                    <el-card shadow="hover">
                        <div class="stat-item">
                            <div class="stat-value">{{ statsData.avgSpeed.toFixed(1) }}km/h</div>
                            <div class="stat-label">平均通行速度</div>
                        </div>
                    </el-card>
                </el-col> -->
            </el-row>
        </div>

        <!-- 主要内容区域 -->
        <div class="content-section">
            <!-- 图表区域 -->
            <el-card shadow="never" class="chart-card">
                <template #header>
                    <div class="card-header">
                        <span>车流量趋势图（进/出隧道方向）</span>
                        <div class="header-actions" v-if="false">
                            <el-select v-model="selectedTunnel" placeholder="选择隧道" @change="updateChart" style="width: 200px">
                                <el-option label="全部隧道" value="" />
                                <el-option v-for="tunnel in tunnelOptions" :key="tunnel.id" :label="tunnel.name" :value="tunnel.name" />
                            </el-select>
                        </div>
                    </div>
                </template>

                <div v-loading="loading" class="chart-container">
                    <div v-if="!loading && chartData.length === 0" class="empty-state">
                        <el-empty description="暂无数据" />
                    </div>
                    <div v-else ref="chartRef" class="chart" style="height: 400px"></div>
                </div>
            </el-card>

            <!-- 数据列表区域 -->
            <el-card shadow="never" class="table-card">
                <template #header>
                    <div class="card-header">
                        <span>详细数据列表</span>
                        <el-button type="text" @click="toggleTableCollapse">
                            {{ isTableCollapsed ? '展开' : '折叠' }}
                            <el-icon><ArrowDown v-if="isTableCollapsed" /><ArrowUp v-else /></el-icon>
                        </el-button>
                    </div>
                </template>

                <el-collapse-transition>
                    <div v-show="!isTableCollapsed">
                        <el-table :data="filteredTableData" v-loading="loading" height="300" stripe border>
                            <el-table-column prop="tunnelName" label="隧道名称" width="180" fixed="left" />
                            <el-table-column prop="timeLabel" label="时间" width="120" />
                            <el-table-column prop="inTruckAmount" label="进隧道流量" width="120" sortable>
                                <template #default="{ row }">
                                    <span class="traffic-number">{{ formatNumber(row.inTruckAmount) }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="outTruckAmount" label="出隧道流量" width="120" sortable>
                                <template #default="{ row }">
                                    <span class="traffic-number">{{ formatNumber(row.outTruckAmount) }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="totalTraffic" label="总流量" width="120" sortable>
                                <template #default="{ row }">
                                    <span class="traffic-number total">{{ formatNumber(row.totalTraffic) }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="inAvgSpeed" label="进隧道平均速度" width="140">
                                <template #default="{ row }">
                                    <span v-if="row.inAvgSpeed > 0">{{ row.inAvgSpeed.toFixed(1) }} km/h</span>
                                    <span v-else class="no-data">-</span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="outAvgSpeed" label="出隧道平均速度" width="140">
                                <template #default="{ row }">
                                    <span v-if="row.outAvgSpeed > 0">{{ row.outAvgSpeed.toFixed(1) }} km/h</span>
                                    <span v-else class="no-data">-</span>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </el-collapse-transition>
            </el-card>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh, ArrowDown, ArrowUp } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { getTraffic24Hour } from '@/api/subProject/health/traffic'
import type { TrafficTunnelData, TrafficHourItem } from '@/api/subProject/health/traffic/types'

// 组件属性
interface Props {
    tunnelName?: string // 用于筛选显示的隧道
}
const props = withDefaults(defineProps<Props>(), {
    tunnelName: ''
})

// 处理隧道名称：去掉方向前缀（南线、北线、东线、西线）
const processedTunnelName = computed(() => {
    if (!props.tunnelName) return ''

    // 去掉方向前缀的正则表达式
    const processed = props.tunnelName.replace(/^(南线|北线|东线|西线)/, '').trim()
    console.log('隧道名称处理:', props.tunnelName, '→', processed)
    return processed
})

// 响应式数据
const loading = ref(false)
const chartData = ref<TrafficTunnelData[]>([])
const dateRange = ref<[Date, Date]>()
const selectedTunnel = ref('')
const isTableCollapsed = ref(true) // 默认折叠
const chartRef = ref<HTMLElement>()
let chartInstance: echarts.ECharts | null = null

// 定时器
let refreshTimer: ReturnType<typeof setInterval>

// 隧道选项
const tunnelOptions = computed(() => {
    return chartData.value.map((tunnel) => ({
        id: tunnel.id,
        name: tunnel.name
    }))
})

// 统计数据
const statsData = computed(() => {
    if (!chartData.value.length) {
        console.log('统计数据计算跳过：无车流数据')
        return null
    }

    let totalInTraffic = 0
    let totalOutTraffic = 0
    let totalSpeedCount = 0
    let totalSpeed = 0

    console.log('开始计算统计数据，隧道数量:', chartData.value.length)

    chartData.value.forEach((tunnel) => {
        if (tunnel.hourItems && Array.isArray(tunnel.hourItems)) {
            tunnel.hourItems.forEach((item) => {
                totalInTraffic += item.inTruckAmount
                totalOutTraffic += item.outTruckAmount
                if (item.inAvgSpeed > 0) {
                    totalSpeed += item.inAvgSpeed
                    totalSpeedCount++
                }
                if (item.outAvgSpeed > 0) {
                    totalSpeed += item.outAvgSpeed
                    totalSpeedCount++
                }
            })
        }
    })

    const stats = {
        totalTunnels: chartData.value.length,
        totalInTraffic,
        totalOutTraffic,
        avgSpeed: totalSpeedCount > 0 ? totalSpeed / totalSpeedCount : 0
    }

    console.log('计算得到的统计数据:', stats)
    return stats
})

// 过滤的数据（根据选择的隧道）
const filteredData = computed(() => {
    if (!selectedTunnel.value) return chartData.value
    return chartData.value.filter((tunnel) => tunnel.name === selectedTunnel.value)
})

// 表格数据
const filteredTableData = computed(() => {
    const tableData: any[] = []

    console.log('计算表格数据，过滤后的数据:', filteredData.value)

    filteredData.value.forEach((tunnel) => {
        if (tunnel.hourItems && Array.isArray(tunnel.hourItems)) {
            tunnel.hourItems.forEach((item) => {
                tableData.push({
                    tunnelName: tunnel.name,
                    timeLabel: `${item.month}月${item.day}日${item.hour}时`,
                    inTruckAmount: item.inTruckAmount,
                    outTruckAmount: item.outTruckAmount,
                    totalTraffic: item.inTruckAmount + item.outTruckAmount,
                    inAvgSpeed: item.inAvgSpeed,
                    outAvgSpeed: item.outAvgSpeed
                })
            })
        } else {
            console.warn('隧道数据缺少hourItems:', tunnel)
        }
    })

    console.log('生成的表格数据条数:', tableData.length)

    // 按时间排序（最新的在前）
    const sortedData = tableData.sort((a, b) => {
        const timeA = new Date(`2025-${a.timeLabel.replace(/月|日|时/g, '-').replace(/-$/, ':00:00')}`)
        const timeB = new Date(`2025-${b.timeLabel.replace(/月|日|时/g, '-').replace(/-$/, ':00:00')}`)
        return timeB.getTime() - timeA.getTime()
    })

    console.log('排序后的表格数据（前3条）:', sortedData.slice(0, 3))
    return sortedData
})

// 获取车流数据
const fetchData = async () => {
    try {
        loading.value = true
        // 使用处理后的隧道名称进行查询
        const query = processedTunnelName.value ? { name: processedTunnelName.value } : undefined
        console.log('车流数据查询参数:', query)
        const response = await getTraffic24Hour(query)
        console.log('获取到车流数据:', response.data)
        // 修复数据访问路径：直接使用 response.data { code, message, data: TrafficTunnelData[] }
        if (response.data && Array.isArray(response.data) && response.data.length > 0) {
            chartData.value = response.data
            console.log('成功设置车流数据，隧道数量:', chartData.value.length)
            await nextTick()
            updateChart()
        } else {
            chartData.value = []
            console.warn('API返回数据为空或格式不正确:', response.data)
            ElMessage.warning('暂无车流数据')
        }
    } catch (error) {
        console.error('获取车流数据失败:', error)
        ElMessage.error('获取车流数据失败')
        chartData.value = []
    } finally {
        loading.value = false
    }
}

// 格式化数字
const formatNumber = (num: number) => {
    return num.toLocaleString()
}

// 更新图表
const updateChart = async () => {
    if (!chartRef.value || !filteredData.value.length) {
        console.log('图表更新跳过：', {
            hasChartRef: !!chartRef.value,
            filteredDataLength: filteredData.value.length
        })
        return
    }

    await nextTick()

    if (!chartInstance) {
        chartInstance = echarts.init(chartRef.value)
    }

    console.log('开始更新图表，过滤后的数据:', filteredData.value)

    // 收集所有隧道的时间数据并去重
    const allTimeItems = filteredData.value.flatMap((tunnel) => tunnel.hourItems)

    // 使用Map去重，以时间唯一标识为key
    const uniqueTimeItems = [...new Map(allTimeItems.map((item) => [`${item.year}-${item.month}-${item.day}-${item.hour}`, item])).values()]

    // 按 year-month-day-hour 进行时间排序（参照CarChartB.vue的排序逻辑）
    const sortedTimeItems = uniqueTimeItems.sort((a, b) => {
        const timeA = new Date(a.year, a.month - 1, a.day, a.hour).getTime()
        const timeB = new Date(b.year, b.month - 1, b.day, b.hour).getTime()
        return timeA - timeB // 从早到晚排序
    })

    // 生成排序后的时间标签
    const hours = sortedTimeItems.map((item) => `${item.month}月${item.day}日${item.hour}时`)

    console.log('排序后的时间轴数据:', hours)
    console.log(
        '时间范围:',
        `${sortedTimeItems[0]?.year}-${sortedTimeItems[0]?.month}-${sortedTimeItems[0]?.day} ${sortedTimeItems[0]?.hour}:00`,
        '到',
        `${sortedTimeItems[sortedTimeItems.length - 1]?.year}-${sortedTimeItems[sortedTimeItems.length - 1]?.month}-${sortedTimeItems[sortedTimeItems.length - 1]?.day} ${sortedTimeItems[sortedTimeItems.length - 1]?.hour}:00`
    )

    const series: any[] = []

    filteredData.value.forEach((tunnel) => {
        // 按排序后的时间顺序生成数据，使用精确的时间匹配
        const inData = sortedTimeItems.map((timeItem) => {
            const item = tunnel.hourItems.find(
                (h) => h.year === timeItem.year && h.month === timeItem.month && h.day === timeItem.day && h.hour === timeItem.hour
            )
            return item ? item.inTruckAmount : 0
        })

        const outData = sortedTimeItems.map((timeItem) => {
            const item = tunnel.hourItems.find(
                (h) => h.year === timeItem.year && h.month === timeItem.month && h.day === timeItem.day && h.hour === timeItem.hour
            )
            return item ? item.outTruckAmount : 0
        })

        series.push(
            {
                name: `${tunnel.name}-进隧道`,
                type: 'line',
                data: inData,
                smooth: true,
                symbol: 'circle',
                symbolSize: 6,
                lineStyle: {
                    color: '#409eff'
                }
            },
            {
                name: `${tunnel.name}-出隧道`,
                type: 'line',
                data: outData,
                smooth: true,
                symbol: 'circle',
                symbolSize: 6,
                lineStyle: {
                    color: '#67c23a',
                    type: 'dashed'
                }
            }
        )
    })

    const option = {
        title: {
            text: selectedTunnel.value ? `${selectedTunnel.value}车流量趋势（进/出方向）` : '全部隧道车流量趋势（进/出方向）',
            left: 'center',
            textStyle: {
                color: '#eef0f4'
            }
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross'
            }
        },
        legend: {
            top: 30,
            type: 'scroll',
            textStyle: {
                color: '#eef0f4'
            }
        },
        grid: {
            left: 60,
            right: 60,
            bottom: 80,
            top: 80
        },
        xAxis: {
            type: 'category',
            data: hours,
            axisLabel: {
                rotate: 45,
                interval: Math.floor(hours.length / 10) || 1
            }
        },
        yAxis: {
            type: 'value',
            name: '车流量',
            axisLabel: {
                formatter: '{value}'
            }
        },
        series: series
    }

    chartInstance.setOption(option)
}

// 刷新数据
const handleRefresh = () => {
    fetchData()
}

// 切换表格折叠状态
const toggleTableCollapse = () => {
    isTableCollapsed.value = !isTableCollapsed.value
}

// 启动自动刷新
const startAutoRefresh = () => {
    refreshTimer = setInterval(() => {
        fetchData()
    }, 30000) // 30秒刷新一次
}

// 组件挂载
onMounted(() => {
    fetchData()
    //startAutoRefresh()

    // 监听窗口大小变化
    window.addEventListener('resize', () => {
        if (chartInstance) {
            chartInstance.resize()
        }
    })
})

// 组件卸载
onBeforeUnmount(() => {
    if (refreshTimer) {
        clearInterval(refreshTimer)
    }
    if (chartInstance) {
        chartInstance.dispose()
    }
})
</script>

<style scoped lang="scss">
.traffic-data-container {
    padding: 20px;
    //background: #f5f7fa;
    min-height: calc(100vh - 120px);
}

.header-section {
    //background: white;
    padding: 0px;
    border-radius: 8px;
    //margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .title-area {
        display: flex;
        align-items: center;
        margin-bottom: 16px;

        h2 {
            margin: 0 12px 0 0;
            color: #f1f3f5;
            font-size: 24px;
            font-weight: 600;
        }
    }

    .query-section {
        .demo-form-inline {
            margin: 0;
        }
    }
}

.stats-cards {
    margin-bottom: 20px;

    .stat-item {
        text-align: center;
        padding: 20px;

        .stat-value {
            font-size: 28px;
            font-weight: bold;
            color: #409eff;
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 14px;
            color: #eef0f4;
        }

        .stat-desc {
            font-size: 12px;
            color: #a0a4a8;
            margin-top: 4px;
        }
    }
}

.content-section {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.chart-card,
.table-card {
    //background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .header-actions {
            display: flex;
            gap: 12px;
            align-items: center;
        }
    }
}

.chart-container {
    .empty-state {
        height: 400px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .chart {
        width: 100%;
    }
}

.traffic-number {
    font-weight: 600;
    color: #409eff;

    &.total {
        color: #67c23a;
    }
}

.no-data {
    color: #c0c4cc;
}

// 响应式适配
@media (max-width: 768px) {
    .traffic-data-container {
        padding: 10px;
    }

    .stats-cards {
        .el-col {
            margin-bottom: 12px;
        }
    }

    .header-section {
        .title-area {
            flex-direction: column;
            align-items: flex-start;

            h2 {
                margin-bottom: 8px;
            }
        }
    }
}
</style>
