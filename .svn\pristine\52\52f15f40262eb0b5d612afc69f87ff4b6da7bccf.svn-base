<!-- 新增养护计划
 workloadComparison 工作量合计数比较，@todo 父组件保存时，需要做这个判断！
-->
<template>
    <div class="p-2">
        <!-- :model="form" :rules="rules" -->
        <el-form ref="taskDefineFormRef" label-width="160px" :model="form" :rules="rules">
            <el-card class="box-card">
                <template v-slot:header>
                    <div class="clearfix">
                        <span>养护计划</span>
                    </div>
                </template>
                <div class="text item">
                    <el-row :gutter="gutter" v-if="isTemp != '1'">
                        <el-col :span="24">
                            <el-form-item label="年份" prop="code">
                                <el-text>{{ form.year }}</el-text>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="gutter" v-if="isTemp == '1'">
                        <el-col :span="8">
                            <el-form-item label="计划作业日期" prop="bgnDate">
                                <el-date-picker
                                    v-model="form.bgnDate"
                                    placeholder="请选择计划作业日期"
                                    format="YYYY-MM-DD"
                                    value-format="YYYY-MM-DD"
                                    :clearable="true"
                                />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="gutter">
                        <el-col :span="8">
                            <el-form-item label="维修养护内容" prop="maintenanceContent">
                                <el-select placeholder="请选择维修养护内容" v-model="form.maintenanceContent">
                                    <el-option v-for="dict in maintenance_content" :key="dict.value" :label="dict.label" :value="dict.value" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="gutter">
                        <el-col :span="8">
                            <el-form-item label="专业类型" prop="speciality">
                                <el-select placeholder="请选择专业类型" v-model="form.speciality" @change="handleSpecialityChange">
                                    <el-option v-for="dict in tnl_specialty" :key="dict.value" :label="dict.label" :value="dict.value" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="gutter" v-if="isTemp == '1'">
                        <el-col :span="8">
                            <el-form-item label="设施设备" prop="devices">
                                <el-input
                                    v-model="selectedDevicesText"
                                    placeholder="请选择设施设备"
                                    readonly
                                    @click="openDeviceSelect"
                                    style="cursor: pointer"
                                >
                                    <template #append>
                                        <el-button @click="openDeviceSelect" type="primary" text>
                                            <el-icon><Search /></el-icon>
                                        </el-button>
                                    </template>
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="gutter">
                        <el-col :span="8">
                            <el-form-item label="养护项目" v-if="isTemp != '1'">
                                <el-tree-select
                                    v-model="selectedCategoryArray"
                                    :data="categoryOptions"
                                    :props="{ value: 'id', label: 'name', children: 'children' }"
                                    value-key="id"
                                    placeholder="请选择"
                                    multiple
                                    :check-strictly="false"
                                    :show-checkbox="true"
                                    check-on-click-node
                                    :filter-leaf-node="true"
                                />
                            </el-form-item>
                            <el-form-item label="养护项目" v-if="isTemp == '1'">
                                <el-tree-select
                                    v-model="selectedCategoryArray"
                                    :data="categoryOptions"
                                    :props="{ value: 'id', label: 'name', children: 'children' }"
                                    value-key="id"
                                    placeholder="请选择"
                                    multiple
                                    :check-strictly="false"
                                    :show-checkbox="true"
                                    check-on-click-node
                                    :filter-leaf-node="true"
                                />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="gutter">
                        <el-col :span="8">
                            <el-form-item label="管理单元">
                                <el-select placeholder="请选择管理单元" :multiple="true" v-model="selectedManageUnitArray">
                                    <el-option v-for="dict in manageUnitOptions" :key="dict.id" :label="dict.name" :value="dict.id" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="gutter">
                        <el-col :span="8">
                            <el-form-item label="计划名称" prop="name">
                                <el-input v-model="form.name" placeholder="请输入年度计划名称" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="gutter">
                        <el-col :span="8">
                            <el-form-item label="备注" prop="remark">
                                <el-input type="textarea" v-model="form.remark" placeholder="请输入备注信息"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <!-- 封道道路选择控件 -->
                    <SealingRoadSelector
                        v-model:sealingType="sealingRoadData.sealingType"
                        v-model:showJobButton="showSealingRoadJobButton"
                        v-model:showSealingTypeRadio="showSealingRoadTypeRadio"
                        v-model:taskResourceItems="sealingRoadData.taskResourceItems"
                        @change="handleSealingRoadChange"
                    />
                    <!-- 此处用频次控件 -->
                    <FrequencySelect
                        ref="frequencyRef"
                        v-model:unit="form.frequencyType"
                        v-model:unitValue="form.frequency"
                        :filterUnits="isTemp === '1' ? 'day' : 'month,week,day'"
                        :max-unit-value="isTemp === '1' ? 1 : undefined"
                        :frequency-selectable="isTemp == '1' ? false : true"
                        :unit-selectable="isTemp == '1' ? false : true"
                    />
                </div>
            </el-card>

            <el-card class="box-card">
                <div class="text item">
                    <el-row :gutter="gutter" justify="center">
                        <el-col :span="4">
                            <el-button :loading="buttonLoading" type="primary" @click="submitForm">保存</el-button>
                        </el-col>
                    </el-row>
                </div>
            </el-card>
        </el-form>

        <!-- 设备选择组件 -->
        <DeviceSelector
            v-model="deviceSelectVisible"
            :project-id="appStore.projectContext.selectedProjectId"
            :specialty="form.speciality"
            @confirm="handleDeviceSelectionConfirm"
        />
    </div>
</template>
<script setup lang="ts">
import FrequencySelect from '../../components/Frequency/FrequencySelect.vue'
import DeviceSelector from '../../components/DeviceSelector.vue'
import SealingRoadSelector from '../../components/SealingRoadSelector/index.vue'
import { addTaskDefine, addTempTaskDefine, updateTaskDefinition, getTaskDefine } from '@/api/plan/taskDefine'
import { getYearOfTaskYear } from '@/api/plan/taskYear'
import { TaskDefineQuery, TaskDefineForm } from '@/api/plan/taskDefine/types'
import { listCategory, listFacilityCategoryTree } from '@/api/common/category'
import { getCategoryUnitViewByProjectAndCategories } from '@/api/common/categoryUnitView'
import type { CategoryUnitViewVO } from '@/api/common/categoryUnitView/types'
import { listProjectManageUnit } from '@/api/project/manageUnit'
import { listEquipment } from '@/api/subProject/basic/equipment'
import { EquipmentQuery } from '@/api/subProject/basic/equipment/types'
import { listFacility } from '@/api/subProject/basic/facility'
import { FacilityQuery } from '@/api/subProject/basic/facility/types'
import { ManageUnitVO } from '@/api/project/manageUnit/types'
import { useRouter, useRoute } from 'vue-router'
import { useAppStore } from '@/store/modules/app'
import { Search } from '@element-plus/icons-vue'

const { proxy } = getCurrentInstance() as ComponentInternalInstance
const { tnl_specialty, maintenance_content } = toRefs<any>(proxy?.useDict('tnl_specialty', 'maintenance_content'))
const router = useRouter()
const showSealingRoadJobButton = ref(false)
const showSealingRoadTypeRadio = ref(false)
const route = useRoute()
const frequencyRef = ref() // 添加对组件的引用
const appStore = useAppStore()
const buttonLoading = ref(false)
const gutter = ref(50) //设置项目表单两列的距离
const selectedCategoryArray = ref<string[]>([])
const selectedManageUnitArray = ref<string[]>([])
const isTemp = ref('0') //是否是临时任务
const isInitialLoading = ref(false) // 初始化加载状态标识

// 设备选择相关
const deviceSelectVisible = ref(false)
const selectedDevices = ref<any[]>([])
const selectedDevicesText = ref('')

// 封道选择器相关数据
const sealingRoadData = ref({
    sealingType: 'part',
    taskResourceItems: []
})

/** 打开设备选择对话框 */
const openDeviceSelect = () => {
    deviceSelectVisible.value = true
}

/** 处理设备选择确认 */
const handleDeviceSelectionConfirm = async (devices: any[]) => {
    console.log('选择的设备:', devices)
    // 更新选中的设备列表
    selectedDevices.value = devices
    console.log('selectedDevices', selectedDevices.value)

    // 更新显示文本
    if (devices.length > 0) {
        const deviceNames = devices.map((device) => device.name).join('、')
        selectedDevicesText.value = deviceNames

        // 只有在临时任务模式下才自动选择养护项目和管理单元
        if (isTemp.value === '1') {
            await autoSelectCategoriesAndUnits(devices)
        }
    } else {
        selectedDevicesText.value = ''
        // 只有在临时任务模式下才清空养护项目和管理单元选择
        if (isTemp.value === '1') {
            selectedCategoryArray.value = []
            selectedManageUnitArray.value = []

            // 清空 taskResource 中的相关数据
            updateTaskResource('equipmentCategorys', [])
            updateTaskResource('manageUnits', [])
        }
    }

    // // 触发表单验证
    // nextTick(() => {
    //     taskDefineFormRef.value?.validateField('devices');
    //     taskDefineFormRef.value?.validateField('equipmentCategorys');
    //     taskDefineFormRef.value?.validateField('manageUnits');
    // });

    proxy?.$modal.msgSuccess(`成功选择 ${devices.length} 个设备`)
}

/**
 * 根据选中的设备自动选择养护项目和管理单元
 * @param devices 选中的设备列表
 */
const autoSelectCategoriesAndUnits = async (devices: any[]) => {
    try {
        if (!devices || devices.length === 0) {
            return
        }

        // 收集所有设备的分类ID和管理单元ID
        const categoryIds = new Set<string>()
        const unitIds = new Set<string>()

        devices.forEach((device) => {
            // 收集分类ID（三级分类）
            if (device.categoryIdThird) {
                categoryIds.add(device.categoryIdThird.toString())
            }

            // 收集管理单元ID
            if (device.unitId) {
                unitIds.add(device.unitId.toString())
            }
        })

        console.log('设备对应的分类ID:', Array.from(categoryIds))
        console.log('设备对应的管理单元ID:', Array.from(unitIds))

        // 自动选择养护项目（分类）
        if (categoryIds.size > 0) {
            const categoryIdArray = Array.from(categoryIds)
            selectedCategoryArray.value = categoryIdArray
            console.log('自动选择的养护项目:', categoryIdArray)
        }

        if (unitIds.size > 0) {
            const unitIdArray = Array.from(unitIds)
            selectedManageUnitArray.value = unitIdArray
            console.log('自动选择的管理单元:', unitIdArray)
        }
        // 自动选择管理单元

        // // 更新 taskResource 中的资源内容
        updateTaskResource('equipmentCategorys', Array.from(categoryIds))
        updateTaskResource('manageUnits', Array.from(unitIds))

        // 显示选中的管理单元名称
        const selectedUnitNames = Array.from(unitIds).map((unitId) => {
            const unit = allManageUnits.value.find((u) => u.id.toString() === unitId)
            return unit ? unit.name : unitId
        })
        // console.log('自动选中的管理单元名称:', selectedUnitNames);
    } catch (error) {
        console.error('自动选择养护项目和管理单元失败:', error)
    }
}

type CategoryOption = {
    id: number
    name: string
    children?: CategoryOption[]
}

// 已弃用的 computed 属性，现在直接使用 selectedCategoryArray 和 selectedManageUnitArray

// 更新 taskResource 的辅助函数
const updateTaskResource = (field: string, value: (string | number)[]) => {
    // if (!form.value.taskResource) {
    //     form.value.taskResource = {
    //         id: undefined,
    //         projectId: undefined,
    //         defineId: undefined,
    //         resouceContent: ''
    //     }
    // }
    // let resourceData: any = {}
    // if (form.value.taskResource.resouceContent) {
    //     try {
    //         resourceData = JSON.parse(form.value.taskResource.resouceContent)
    //     } catch {
    //         resourceData = {}
    //     }
    // }
    // resourceData[field] = value
    // form.value.taskResource.resouceContent = JSON.stringify(resourceData)
}

const taskDefineFormRef = ref<ElFormInstance>()

/**
 * 过滤只显示项目中存在设备的分类
 * @param categories 分类树数据
 * @returns 过滤后的分类树数据
 */
const filterCategoriesWithExistingEquipment = async (categories: CategoryOption[]): Promise<CategoryOption[]> => {
    try {
        // 获取项目中所有设备的分类ID
        const equipmentQuery: EquipmentQuery = {
            projectId: appStore.projectContext.selectedProjectId as string,
            pageNum: 1,
            pageSize: 10000, // 获取所有数据
            kind: 'equipment',
            configedInspection: ''
        }

        const equipmentRes = await listEquipment(equipmentQuery)
        const equipments = equipmentRes.rows || []

        // 提取所有设备的分类ID（三级分类）
        const existingCategoryIds = new Set<string>()
        equipments.forEach((equipment) => {
            if (equipment.categoryIdThird) {
                existingCategoryIds.add(equipment.categoryIdThird.toString())
            }
        })

        console.log('项目中存在的设备分类ID:', Array.from(existingCategoryIds))

        // 递归过滤分类树，只保留有设备的分类及其父级分类
        const filterTree = (nodes: CategoryOption[]): CategoryOption[] => {
            return nodes.filter((node) => {
                // 如果有子节点，递归过滤子节点
                if (node.children && node.children.length > 0) {
                    node.children = filterTree(node.children)
                    // 如果过滤后还有子节点，则保留该节点
                    return node.children.length > 0
                } else {
                    // 叶子节点：检查是否有对应的设备
                    return existingCategoryIds.has(node.id.toString())
                }
            })
        }

        return filterTree(categories)
    } catch (error) {
        console.error('过滤设备分类失败:', error)
        return categories // 出错时返回原始数据
    }
}

/**
 * 过滤只显示项目中存在设施的分类
 * @param categories 分类树数据
 * @param specialty 专业类型
 * @returns 过滤后的分类树数据
 */
const filterCategoriesWithExistingFacilities = async (categories: CategoryOption[], specialty: string): Promise<CategoryOption[]> => {
    try {
        // 获取项目中所有设施的分类ID
        const facilityQuery: FacilityQuery = {
            projectId: appStore.projectContext.selectedProjectId as string,
            pageNum: 1,
            pageSize: 10000 // 获取所有数据
        }

        const facilityRes = await listFacility(facilityQuery)
        const facilities = facilityRes.rows || []

        // 提取所有设施的分类ID（三级分类）
        const existingCategoryIds = new Set<string>()
        facilities.forEach((facility) => {
            if (facility.categoryIdThird) {
                existingCategoryIds.add(facility.categoryIdThird.toString())
            }
        })

        console.log('项目中存在的设施分类ID:', Array.from(existingCategoryIds))

        // 递归过滤分类树，只保留有设施的分类及其父级分类
        const filterTree = (nodes: CategoryOption[]): CategoryOption[] => {
            return nodes.filter((node) => {
                // 如果有子节点，递归过滤子节点
                if (node.children && node.children.length > 0) {
                    node.children = filterTree(node.children)
                    // 如果过滤后还有子节点，则保留该节点
                    return node.children.length > 0
                } else {
                    // 叶子节点：检查是否有对应的设施
                    return existingCategoryIds.has(node.id.toString())
                }
            })
        }

        return filterTree(categories)
    } catch (error) {
        console.error('过滤设施分类失败:', error)
        return categories // 出错时返回原始数据
    }
}

// 处理专业类型变化
const handleSpecialityChange = async (value: string) => {
    console.log('专业类型选择变化:', value)
    // 清空已选择的养护项目
    selectedCategoryArray.value = []
    // 清空已选择的管理单元
    selectedManageUnitArray.value = []
    // 清空已选择的设备
    selectedDevices.value = []
    selectedDevicesText.value = ''

    // 重新获取养护项目分类
    await getMaintainContentTreeselect()
    form.value.taskResourceItems = []
}

const initFormData: TaskDefineForm = {
    id: undefined,
    projectId: undefined,
    yearTaskId: undefined,
    name: undefined,
    taskType: undefined,
    speciality: undefined,
    year: undefined,
    frequencyType: undefined,
    frequency: undefined,
    frequencyData: undefined,
    bgnDate: undefined,
    endDate: undefined,
    tempTask: undefined,
    maintenanceContent: undefined,
    equipmentCategorys: [],
    manageUnits: [],

    taskResource: {
        id: undefined,
        projectId: undefined,
        defineId: undefined,
        resouceContent: ''
    },
    taskResourceItems: [],
    status: undefined,
    remark: undefined,
    publishState: 'temp'
}

const data = reactive<PageData<TaskDefineForm, TaskDefineQuery>>({
    form: { ...initFormData },
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectId: undefined,
        yearTaskId: undefined,
        name: undefined,
        taskType: undefined,
        speciality: undefined,
        year: undefined,
        frequencyType: undefined,
        frequency: undefined,
        maintenanceContent: undefined,
        status: undefined,
        params: {}
    },
    rules: {
        // id: [{ required: true, message: '不能为空', trigger: 'blur' }],
        name: [{ required: true, message: '年度计划名称不能为空', trigger: 'blur' }],
        maintenanceContent: [{ required: true, message: '维修养护内容不能为空', trigger: 'change' }],
        year: [{ required: true, message: '年份不能为空', trigger: 'blur' }],
        //taskType: [{ required: true, message: '任务类型：巡检、养护、封道，字典项：plan_type不能为空', trigger: 'change' }],
        speciality: [{ required: true, message: '专业类型不能为空', trigger: 'change' }],
        frequencyType: [{ required: true, message: '频次类型不能为空', trigger: 'change' }],
        frequency: [{ required: true, message: '频次不能为空', trigger: 'blur' }],
        status: [{ required: true, message: '任务定义审核/发布状态不能为空', trigger: 'change' }],
        equipmentCategorys: [
            {
                required: true,
                message: '养护项目不能为空'
                // validator: (rule: any, value: any, callback: any) => {
                //     if (!selectedDeviceCategories.value || selectedDeviceCategories.value.length === 0) {
                //         callback(new Error('养护项目不能为空'));
                //     } else {
                //         callback();
                //     }
                // },
            }
        ],
        manageUnits: [
            {
                required: true,
                message: '管理单元不能为空'
                // validator: (rule: any, value: any, callback: any) => {
                //     if (!selectedManageUnits.value || selectedManageUnits.value.length === 0) {
                //         callback(new Error('管理单元不能为空'));
                //     } else {
                //         callback();
                //     }
                // },
            }
        ],
        devices: [
            {
                required: true,
                message: '设施设备不能为空',
                trigger: 'change',
                validator: (rule: any, value: any, callback: any) => {
                    // 只有在临时任务模式下才验证设备选择
                    if (isTemp.value === '1') {
                        if (!selectedDevices.value || selectedDevices.value.length === 0) {
                            callback(new Error('设施设备不能为空'))
                        } else {
                            callback()
                        }
                    } else {
                        callback()
                    }
                }
            }
        ],
        bgnDate: [{ required: true, message: '作业计划执行日期不能为空', trigger: 'blur' }]
    }
})

const { queryParams, form, rules } = toRefs(data)
const categoryOptions = ref<CategoryOption[]>([])
const allManageUnits = ref<ManageUnitVO[]>([])
const manageUnitOptions = ref<ManageUnitVO[]>([])
const yearTaskId = ref<string>('')

/** 提交按钮 */
const submitForm = () => {
    if (isTemp.value == '1') {
        form.value.endDate = form.value.bgnDate
        form.value.tempTask = 'YES'
    } else {
        form.value.bgnDate = null
        form.value.endDate = null
        form.value.tempTask = 'NO'
    }

    form.value.taskType = 'curing'
    form.value.projectId = appStore.projectContext.selectedProjectId
    form.value.frequencyType = frequencyRef.value?.unit
    console.log('form.value.frequencyType', frequencyRef.value?.unit)

    form.value.frequency = frequencyRef.value?.value
    form.value.year = getYearOfTaskYear()

    // 合并原有的 taskResourceItems 和封道相关的数据
    if (isTemp.value == '1') {
        // 临时任务：合并设备数据和封道数据
        const deviceItems = selectedDevices.value.map((device) => ({
            deviceId: device.id,
            unitId: device.unitId,
            deviceCategoryId: device.categoryIdThird,
            specialty: device.specialty,
            // 添加标识字段，区分数据来源
            dataType: 'device'
        }))

        // 获取封道选择器的数据
        const sealingItems = sealingRoadData.value.taskResourceItems.map((item) => ({
            roadId: item.roadId,
            startStake: item.startStake,
            endStake: item.endStake,
            jobIdList: Array.isArray(item.jobIdList) ? item.jobIdList.join(',') : item.jobIdList || '',
            // 添加标识字段，区分数据来源
            dataType: 'road'
        }))

        // 合并所有数据
        form.value.taskResourceItems = [...deviceItems, ...sealingItems]

        // // 临时任务模式下，清空taskResource以避免重复插入
        // form.value.taskResource = {
        //     id: undefined,
        //     projectId: undefined,
        //     defineId: undefined,
        //     resouceContent: ''
        // }
    } else {
        // 年度计划：合并分类管理单元数据和封道数据
        // const categoryUnitItems = form.value.taskResourceItems.map((item) => ({
        //     ...item,
        //     dataType: 'category_unit'
        // }))
        const categoryUnitItems = []
        selectedCategoryArray.value.forEach((categoryId, categoryIndex) => {
            selectedManageUnitArray.value.forEach((unitId, unitIndex) => {
                categoryUnitItems.push({
                    deviceCategoryId: categoryId,
                    unitId: unitId,
                    dataType: 'category_unit'
                })
            })
        })
        console.log(categoryUnitItems)
        // 获取封道选择器的数据
        const sealingItems = sealingRoadData.value.taskResourceItems.map((item) => ({
            roadId: item.roadId,
            startStake: item.startStake,
            endStake: item.endStake,
            jobIdList: Array.isArray(item.jobIdList) ? item.jobIdList.join(',') : item.jobIdList || '',
            dataType: 'road'
        }))

        // 合并所有数据
        form.value.taskResourceItems = [...categoryUnitItems, ...sealingItems]

        console.log('合并后的taskResourceItems:', form.value.taskResourceItems)
    }

    //设置频次数据
    if (form.value.frequencyType == 'week') {
        form.value.frequencyData = JSON.stringify({
            tableData: frequencyRef.value?.tableData,
            scheduleData: frequencyRef.value?.weekScheduleData
        })
    } else if (form.value.frequencyType == 'month') {
        form.value.frequencyData = JSON.stringify({
            tableData: frequencyRef.value?.tableData,
            scheduleData: frequencyRef.value?.monthScheduleData
        })
    } else if (form.value.frequencyType == 'day') {
        form.value.frequencyData = JSON.stringify({
            tableData: frequencyRef.value?.tableData,
            scheduleData: frequencyRef.value?.dayScheduleData
        })
    }

    console.log('提交前的完整表单数据:', form.value)

    taskDefineFormRef.value?.validate(async (valid: boolean) => {
        if (valid) {
            if (form.value.id) {
                await updateTaskDefinition(form.value).finally(() => (buttonLoading.value = false))
            } else {
                if (isTemp.value == '1') {
                    form.value.tempTask = 'YES'
                    await addTempTaskDefine(form.value).finally(() => (buttonLoading.value = false))
                } else {
                    form.value.tempTask = 'NO'
                    await addTaskDefine(form.value).finally(() => (buttonLoading.value = false))
                }
            }
            router.push({
                path: '/subProject/plan/defineList',
                query: { yearTaskId: yearTaskId.value }
            })
            proxy?.$modal.msgSuccess('操作成功')
            //年度计划列表
            // await getList();
        }
    })
}

/** 查询category下拉树结构 */
const getMaintainContentTreeselect = async () => {
    try {
        let res: any
        let treeData: CategoryOption[] = []

        if (form.value.speciality === 'electric') {
            // 机电系统：获取设备分类
            const queryParams = {
                projectId: appStore.projectContext.selectedProjectId,
                kind: 'equipment'
            }
            res = await listCategory(queryParams)
            treeData = proxy?.handleTree<CategoryOption>(res.data, 'id', 'parentId') || []

            // 过滤只显示项目中存在设备的分类
            treeData = await filterCategoriesWithExistingEquipment(treeData)
        } else if (form.value.speciality) {
            // 其他专业：获取对应专业的设施分类
            res = await listFacilityCategoryTree(form.value.speciality)
            // listFacilityCategoryTree返回的已经是树形结构
            treeData = res.data || []

            // 过滤只显示项目中存在设施的分类
            treeData = await filterCategoriesWithExistingFacilities(treeData, form.value.speciality)
        } else {
            // 未选择专业类型时，清空选项
            treeData = []
        }

        categoryOptions.value = treeData
        console.log('获取养护项目分类成功，专业类型:', form.value.speciality, '分类数量:', categoryOptions.value.length)
    } catch (error) {
        console.error('获取养护项目分类失败:', error)
        categoryOptions.value = []
    }
}

/** 获取详情 */
const getDetail = async (id: string) => {
    // 设置加载状态
    isInitialLoading.value = true

    const res = await getTaskDefine(id)
    const taskDefine = res.data

    // 先设置基本信息
    form.value = {
        ...initFormData,
        id: taskDefine.id,
        projectId: taskDefine.projectId,
        yearTaskId: taskDefine.yearTaskId,
        name: taskDefine.name,
        taskType: taskDefine.taskType,
        speciality: taskDefine.speciality,
        year: taskDefine.year,
        frequencyType: taskDefine.frequencyType,
        frequency: taskDefine.frequency,
        frequencyData: taskDefine.frequencyData,
        maintenanceContent: taskDefine.maintenanceContent,
        status: taskDefine.status,
        remark: taskDefine.remark,
        equipmentCategorys: [],
        manageUnits: [],
        taskResourceItems: taskDefine.taskResourceItems
        // taskResource: taskDefine.taskResource
        //     ? {
        //           id: taskDefine.taskResource.id,
        //           projectId: taskDefine.taskResource.projectId?.toString(),
        //           unitId: taskDefine.taskResource.unitId?.toString(),
        //           resouceContent: taskDefine.taskResource.resouceContent,
        //           defineId: taskDefine.taskResource.defineId?.toString()
        //       }
        //     : undefined
    }

    // 设置选中值
    await getMaintainContentTreeselect()

    // 设置资源内容
    if (taskDefine.taskResourceItems) {
        try {
            // 只从 dataType 为 'category_unit' 的数据中提取
            const categoryUnitItems = taskDefine.taskResourceItems.filter((item) => item.dataType === 'category_unit')

            // 从 category_unit 类型数据中提取 deviceCategoryId
            const deviceCategoryIds = categoryUnitItems.map((item) => item.deviceCategoryId).filter((id) => id)
            selectedCategoryArray.value = [...new Set(deviceCategoryIds)]

            // 从 category_unit 类型数据中提取 unitId
            const unitIds = categoryUnitItems.map((item) => item.unitId).filter((id) => id)
            selectedManageUnitArray.value = [...new Set(unitIds)]

            // 处理封道线路信息和位置桩号回填
            handleSealingRoadDataBackfill(taskDefine.taskResourceItems)

            // 等待下一个tick确保DOM更新完成
            await nextTick()
            console.log('编辑模式下设置选中的养护项目:', selectedCategoryArray.value)
            console.log('编辑模式下设置选中的管理单元:', selectedManageUnitArray.value)
        } catch (error) {
            console.error('解析资源内容失败:', error)
        }
    }

    // 设置频次组件
    if (taskDefine.frequencyType && taskDefine.frequency) {
        if (frequencyRef.value) {
            // 设置加载状态，避免监听器触发重新计算
            frequencyRef.value.setLoadingState(true)

            // 先设置频次数据（tableData、scheduleData等）
            if (taskDefine.frequencyData) {
                try {
                    const frequencyData = JSON.parse(taskDefine.frequencyData)
                    frequencyRef.value.tableData = frequencyData.tableData || []
                    if (taskDefine.frequencyType === 'week') {
                        frequencyRef.value.weekScheduleData = frequencyData.scheduleData || []
                    } else if (taskDefine.frequencyType === 'month') {
                        frequencyRef.value.monthScheduleData = frequencyData.scheduleData || []
                    } else if (taskDefine.frequencyType === 'day') {
                        frequencyRef.value.dayScheduleData = frequencyData.scheduleData || []
                    }
                } catch (error) {
                    console.error('解析频次数据失败:', error)
                }
            }

            // 直接设置unit和value，此时监听器会被跳过
            frequencyRef.value.unit = taskDefine.frequencyType
            frequencyRef.value.value = taskDefine.frequency

            // 数据加载完毕，恢复正常状态
            nextTick(() => {
                frequencyRef.value.setLoadingState(false)
            })
        } else {
            console.warn('FrequencySelect 组件引用未找到')
        }
    }

    // 初始化完成，恢复正常状态
    nextTick(() => {
        isInitialLoading.value = false
    })
}

/**
 * 处理养护项目选择变化
 * @param value 选中的分类ID数组
 */
const handleCategoriesChange = async (value: (string | number)[]) => {
    console.log('养护项目选择变化:', value)

    // 只在年度计划模式下执行
    if (isTemp.value !== '1') {
        try {
            // 调用getCategoryUnitViewByProjectAndCategories获取categoryUnitViewVo列表
            const projectId = appStore.projectContext.selectedProjectId
            const categoryIds = value.map((id) => String(id))

            if (projectId && categoryIds.length > 0) {
                const response = await getCategoryUnitViewByProjectAndCategories(projectId, categoryIds)
                console.log('获取到的categoryUnitView列表:', response.data)

                // 将CategoryUnitViewVO转换为TaskResourceItemBo格式并赋给form.value.taskResourceItems
                const categoryUnitViews = (response.data || []) as CategoryUnitViewVO[]
                const taskResourceItems = categoryUnitViews.map((item) => ({
                    projectId: projectId,
                    unitId: item.unitId,
                    deviceCategoryId: item.categoryId
                }))

                form.value.taskResourceItems = taskResourceItems

                // 提取unitId列表并去重，然后绑定到管理单元选择控件
                const unitIds = [...new Set(taskResourceItems.map((item) => item.unitId))].filter((id) => id)
                selectedManageUnitArray.value = unitIds

                console.log('自动选择的管理单元:', unitIds)
            } else {
                form.value.taskResourceItems = []
                selectedManageUnitArray.value = []
            }
        } catch (error) {
            console.error('获取categoryUnitView列表失败:', error)
            form.value.taskResourceItems = []
        }
    }
}

// 监听养护项目选择变化
watch(
    () => selectedCategoryArray.value,
    async (newValue, oldValue) => {
        console.log('养护项目选择变化:', { newValue, oldValue })

        // 添加加载状态判断，初始化时不触发自动关联
        if (newValue && newValue.length > 0 && newValue !== oldValue && isTemp.value !== '1' && !isInitialLoading.value) {
            // 确保类型转换正确
            const categoryIds = newValue.map((id) => String(id))
            await handleCategoriesChange(categoryIds)
        }
    },
    { deep: true }
)

// 监听专业类型变化
watch(
    () => form.value.speciality,
    async (newSpecialty, oldSpecialty) => {
        console.log('专业类型变化:', { oldSpecialty, newSpecialty })

        if (newSpecialty !== oldSpecialty) {
            // 清空已选择的养护项目
            selectedCategoryArray.value = []
            // 清空已选择的管理单元
            selectedManageUnitArray.value = []
            // 清空已选择的设备
            selectedDevices.value = []
            selectedDevicesText.value = ''

            // 重新获取养护项目分类
            await getMaintainContentTreeselect()
        }
    }
)

/**
 * 处理封道线路信息和位置桩号回填
 * @param taskResourceItems 任务资源项列表
 */
const handleSealingRoadDataBackfill = (taskResourceItems: any[]) => {
    try {
        // 从taskResourceItems中提取封道相关数据（dataType为'road'或包含roadId的数据）
        const sealingItems = taskResourceItems.filter(
            (item) => item.dataType === 'road' || (item.roadId && (item.startStake !== undefined || item.endStake !== undefined))
        )

        if (sealingItems.length > 0) {
            // 构建封道选择器需要的数据格式
            const sealingTaskResourceItems = sealingItems.map((item) => ({
                roadId: item.roadId,
                startStake: item.startStake,
                endStake: item.endStake,
                jobIdList: typeof item.jobIdList === 'string' ? item.jobIdList.split(',').filter((id: string) => id.trim()) : item.jobIdList || [],
                dataType: 'road'
            }))

            // 设置封道选择器数据
            sealingRoadData.value = {
                sealingType: 'part', // 计划中封闭类型都为部分封闭
                taskResourceItems: sealingTaskResourceItems
            }

            console.log('成功回填封道线路信息和位置桩号:', sealingTaskResourceItems)
        } else {
            // 没有封道数据时，保持默认状态
            sealingRoadData.value = {
                sealingType: 'part',
                taskResourceItems: []
            }
            console.log('未找到封道数据，保持默认状态')
        }
    } catch (error) {
        console.error('回填封道数据失败:', error)
        // 出错时保持默认状态
        sealingRoadData.value = {
            sealingType: 'part',
            taskResourceItems: []
        }
    }
}

/**
 * 处理封道选择器变化
 * @param value 封道选择器的数据
 */
const handleSealingRoadChange = (value: any) => {
    sealingRoadData.value = value
    console.log('封道选择器数据变化:', value)
}

onMounted(async () => {
    //getList();
    const queryId = route.query.yearTaskId
    yearTaskId.value = Array.isArray(queryId) ? queryId[0] : queryId || ''
    isTemp.value = Array.isArray(route.query.isTemp) ? route.query.isTemp[0] : route.query.isTemp || '0'
    if (yearTaskId.value) {
        form.value.yearTaskId = yearTaskId.value
    }

    const projectId = appStore.projectContext.selectedProjectId
    allManageUnits.value = (await listProjectManageUnit(projectId)).data

    manageUnitOptions.value = allManageUnits.value
    form.value.year = getYearOfTaskYear()

    // 获取taskId参数并加载详情
    const id = route.query.id
    const taskId = Array.isArray(id) ? id[0] : id || ''
    if (taskId) {
        // 确保所有组件都已挂载后再加载详情
        nextTick(() => {
            getDetail(taskId)
        })
    } else {
        // 如果不是编辑模式，则直接加载分类选项
        getMaintainContentTreeselect()
    }
})
</script>

<style lang="scss" scoped>
.box-card {
    margin-top: 10px;
}
</style>
