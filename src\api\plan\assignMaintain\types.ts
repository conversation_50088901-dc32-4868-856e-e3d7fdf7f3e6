import { TaskVO, TaskForm, NextAssigneeForm } from '@/api/plan/task/types'
import { TaskResourceItemVO } from '../taskResourceItem/types'
import { TaskResourceItemVo } from '../taskDefine/types'

export interface AssignMaintainVO {
    /**
     *
     */
    id: string | number

    /**
     * day任务id
     */
    taskId: string | number

    /**
     * 作业名称，来自task实体
     */
    name: string

    /**
     * 作业开始时间
     */
    bgnDate: string

    /**
     * 作业结束时间
     */
    endDate: string

    /**
     * 作业类型，字典项：
     */
    jobType: string

    /**
     * 安全员-id
     */
    safeManId: string | number

    /**
     * 带班负责人
     */
    managerId: string | number

    /**
     * 从业单位
     */
    companys: string

    /**
     * 班组
     */
    teams: string

    /**
     * 车辆
     */
    cars: string

    /**
     * 资源-JSON
     */
    resource: string

    /**
     * 执行时长
     */
    duration: number

    /**
     * 安全交底图片
     */
    safeImages: string

    /**
     * 实际开工日期
     */
    actualBgnDate: string

    /**
     * 实际结束日期
     */
    actualEndDate: string

    /**
     *
     */
    actualTeams: string

    /**
     *
     */
    actualCars: string

    /**
     * 实际耗费物资
     */
    actualResource: string

    /**
     * 实际时长
     */
    actualDuration: number

    /**
     *
     */
    tnlAssignMaintaincol: string

    /**
     * 执行相关图片
     */
    images: string

    /**
     * 完成情况及自检意见
     */
    description: string
}

export interface AssignMaintainForm extends BaseEntity {
    /**
     *
     */
    id?: string | number

    /**
     * day任务id
     */
    taskId?: string | number

    /**
     * 作业名称，来自task实体
     */
    name?: string

    /**
     * 作业开始时间
     */
    bgnDate?: string

    /**
     * 作业结束时间
     */
    endDate?: string

    /**
     * 作业类型，字典项：
     */
    jobType?: string

    /**
     * 安全员-id
     */
    safeManId?: string | number

    /**
     * 带班负责人
     */
    managerId?: string | number

    /**
     * 从业单位
     */
    companys?: string

    /**
     * 班组
     */
    teams?: string

    /**
     * 车辆
     */
    cars?: string

    /**
     * 资源-JSON
     */
    resource?: string
    resourceList?: AssignMaintainResourceVo[]

    /**
     * 执行时长
     */
    duration?: number

    /**
     * 安全交底图片
     */
    safeImages?: string

    /**
     * 实际开工日期
     */
    actualBgnDate?: string

    /**
     * 实际结束日期
     */
    actualEndDate?: string

    /**
     *
     */
    actualTeams?: string

    /**
     *
     */
    actualCars?: string

    /**
     * 实际耗费物资
     */
    actualResource?: string
    actualResourceList?: AssignMaintainResourceVo[]

    /**
     * 实际时长
     */
    actualDuration?: number

    /**
     * 执行相关图片
     */
    images?: string

    /**
     * 完成情况及自检意见
     */
    description?: string
}
export interface AssignMaintainResource {
    /**
     * 估算物资或者实际使用物资的物资ID
     */
    resourceId: string

    /**
     * 估算物资或者实际使用物资的数量
     */
    quantity: number
}

export interface AssignMaintainQuery extends PageQuery {
    /**
     * day任务id
     */
    taskId?: string | number

    /**
     * 作业名称，来自task实体
     */
    name?: string

    /**
     * 作业开始时间
     */
    bgnDate?: string

    /**
     * 作业结束时间
     */
    endDate?: string

    /**
     * 作业类型，字典项：
     */
    jobType?: string

    /**
     * 安全员-id
     */
    safeManId?: string | number

    /**
     * 带班负责人
     */
    managerId?: string | number

    /**
     * 从业单位
     */
    companys?: string

    /**
     * 班组
     */
    teams?: string

    /**
     * 车辆
     */
    cars?: string

    /**
     * 资源-JSON
     */
    resource?: string

    /**
     * 执行时长
     */
    duration?: number

    /**
     * 安全交底图片
     */
    safeImages?: string

    /**
     * 实际开工日期
     */
    actualBgnDate?: string

    /**
     * 实际结束日期
     */
    actualEndDate?: string

    /**
     *
     */
    actualTeams?: string

    /**
     *
     */
    actualCars?: string

    /**
     * 实际耗费物资
     */
    actualResource?: string

    /**
     * 实际时长
     */
    actualDuration?: number

    /**
     *
     */
    tnlAssignMaintaincol?: string

    /**
     * 执行相关图片
     */
    images?: string

    /**
     * 完成情况及自检意见
     */
    description?: string

    /**
     * 日期范围参数
     */
    params?: any
}

// TaskAssignment相关类型定义

export interface AssignMaintainFlowForm {
    /**
     * 任务基本信息
     */
    task: TaskForm
    expired: boolean

    /**
     * 维养分配信息
     */
    assignMaintain: AssignMaintainForm

    /**
     * 下一环节处理人信息
     */
    nextAssignee?: NextAssigneeForm

    /**
     * 是否为延期操作，默认为false
     */
    isDelay?: boolean
}

// AssignMaintainFlowVo - 对应后端的AssignMaintainFlowVo
export interface AssignMaintainFlowVo {
    /**
     * 任务基本信息
     */
    task?: TaskVO
    expired: boolean
    /**
     * 维养分配信息
     */
    assignMaintain?: AssignMaintainVO
}
export interface AssignMaintainResourceVo {
    resourceId: string
    quantity: number
}

/**
 * 维养详情VO
 * 对应Java类 MaintainDetailVo
 */
export interface MaintainDetailVO {
    statusLabel?: string
    /** 计划名称 */
    title?: string
    /** 任务子类型 */
    taskSubType?: string
    /** 专业类型 */
    speciality?: string
    /** 管理单元 */
    unitNameLabel?: string
    /** 养护内容 */
    maintainContent?: string
    /** 养护项目名称 */
    maintainItemNames?: string
    maintainDevices?: string
    /** 设备名称 */
    deviceNameLabel?: string
    /** 计划日期 */
    planDate?: string
    /** 计划开始时间 */
    planBeginTime?: string
    /** 计划结束时间 */
    planEndTime?: string
    /** 频率 */
    frequency?: string
    /** 频率单位 */
    frequencyUnit?: string
    taskResourceItems: TaskResourceItemVO[]
}
