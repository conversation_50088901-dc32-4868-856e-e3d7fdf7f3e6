<!-- 维养任务指派 -->
<template>
    <div class="p-2" style="margin-top: -10px">
        <el-card class="box-card">
            <template v-slot:header>
                <div class="clearfix">
                    <span>作业单信息</span>
                </div>
            </template>
            <!-- 作业单任务基本信息 -->

            <BaseInfoMaintain :id="taskId" from="task" />
        </el-card>
        <TaskDelayHistory :taskId="taskId" />
        <el-card class="box-card">
            <template v-slot:header>
                <div class="clearfix">
                    <span>作业进度</span>
                </div>
            </template>
            <div class="text item">
                <el-form ref="projectFormRef" :model="form" :rules="rules" label-width="160px">
                    <!-- 指派指派环节由安全交底时通过班组进行指派 -->
                    <!-- <div v-if="form.task.currentStatus == 'START'">
                        <el-row :gutter="gutter">
                            <el-col :span="6">

                                <el-form-item label="作业开始时间" prop="task.bgnDate">
                                    <el-date-picker
                                        v-model="form.task.bgnDate"
                                        type="datetime"
                                        placeholder="请选择作业开始时间"
                                        format="YYYY-MM-DD HH:mm:ss"
                                        value-format="YYYY-MM-DD HH:mm:ss"
                                        @change="handleStartDateChange"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">

                                <el-form-item label="作业结束时间" prop="task.endDate">
                                    <el-date-picker
                                        v-model="form.task.endDate"
                                        type="datetime"
                                        placeholder="请选择作业结束时间"
                                        format="YYYY-MM-DD HH:mm:ss"
                                        value-format="YYYY-MM-DD HH:mm:ss"
                                        :disabled-date="disabledEndDate"
                                        @change="handleEndDateChange"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="gutter">
                            <el-col :span="6">

                                <el-form-item label="作业类型" prop="assignMaintain.jobType">
                                    <el-select v-model="form.assignMaintain.jobType" placeholder="请选择作业类型" clearable>
                                        <el-option v-for="dict in job_type" :key="dict.value" :label="dict.label" :value="dict.value" />
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">

                                <el-form-item label="安全员" prop="assignMaintain.safeManId">
                                    <ManagersSelector v-model="form.assignMaintain.safeManId" post-code="aqy" placeholder="请选择安全员" clearable />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="gutter">
                            <el-col :span="6">

                                <el-form-item label="带班负责人" prop="nextAssignee.nextAssignees">
                                    <ManagersSelector
                                        v-model="form.nextAssignee.nextAssignees"
                                        placeholder="请选择带班负责人"
                                        clearable
                                        style="width: 100%"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">

                                <el-form-item label="班组" prop="assignMaintain.teams">
                                    <TeamSelector v-model="form.assignMaintain.teams" placeholder="请选择班组" />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="gutter">
                            <el-col :span="12">

                                <el-form-item label="估计车辆" prop="assignMaintain.cars">
                                    <CarSelector v-model="form.assignMaintain.cars" placeholder="请选择车辆" clearable multiple show-debug-info />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="gutter">
                            <el-col :span="12">

                                <el-form-item label="估算物资" prop="assignMaintain.resourceList">

                                    <div class="resource-list">
                                        <div v-for="(resource, index) in form.assignMaintain.resourceList" :key="index" class="resource-item">

                                            <el-select
                                                v-model="resource.resourceId"
                                                placeholder="请选择物资"
                                                clearable
                                                style="width: 200px; margin-right: 10px"
                                            >
                                                <el-option
                                                    v-for="resourceType in resourceTypes"
                                                    :key="resourceType.id"
                                                    :label="resourceType.name"
                                                    :value="resourceType.id"
                                                />
                                            </el-select>


                                            <el-input-number v-model="resource.quantity" :min="1" placeholder="数量" />


                                            <span class="unit-display">
                                                {{ getResourceTypeInfo(resource.resourceId).unit }}
                                            </span>


                                            <el-button
                                                type="danger"
                                                size="small"
                                                @click="removeResourceItem(index)"
                                                :disabled="form.assignMaintain.resourceList!.length <= 1"
                                            >
                                                删除
                                            </el-button>
                                        </div>


                                        <el-button type="primary" size="small" @click="addResourceItem" class="add-resource-btn" plain>
                                            + 添加物资
                                        </el-button>
                                    </div>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="gutter">
                            <el-col :span="6">
                                <el-form-item label="执行时长" prop="assignMaintain.duration">
                                    <el-input-number
                                        v-model="form.assignMaintain.duration"
                                        :min="1"
                                        placeholder="请输入执行时长（小时）"
                                        style="width: 100%"
                                    ></el-input-number>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div> -->

                    <!-- 安全交底 -->
                    <div v-if="form.task.currentStatus == 'Safety_Briefing'">
                        <el-row :gutter="gutter">
                            <el-row :gutter="gutter">
                                <el-col :span="12">
                                    <el-form-item label="安全交底图片" prop="safeImages">
                                        <imageUpload v-model="form.assignMaintain.safeImages" @update:modelValue="handleSafeUploadChange" />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-row>
                    </div>
                    <!-- 执行 -->
                    <div v-if="form.task.currentStatus == 'Execute'">
                        <el-row :gutter="gutter">
                            <el-col :span="6">
                                <el-form-item label="实际开工日期" prop="task.taskStartDate">
                                    <el-date-picker
                                        clearable
                                        v-model="form.task.taskStartDate"
                                        type="datetime"
                                        format="YYYY-MM-DD HH:mm:ss"
                                        value-format="YYYY-MM-DD HH:mm:ss"
                                        placeholder="请选择实际开工日期"
                                        @change="handleActualStartDateChange"
                                    >
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="实际结束日期" prop="task.taskFinishDate">
                                    <el-date-picker
                                        v-model="form.task.taskFinishDate"
                                        clearable
                                        type="datetime"
                                        format="YYYY-MM-DD HH:mm:ss"
                                        value-format="YYYY-MM-DD HH:mm:ss"
                                        placeholder="请选择实际结束日期"
                                        @change="handleActualEndDateChange"
                                    >
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="gutter">
                            <el-col :span="6">
                                <el-form-item label="实际班组" prop="assignMaintain.teams">
                                    <TeamSelector v-model="form.assignMaintain.teams" placeholder="请选择实际班组" clearable />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="gutter">
                            <el-col :span="12">
                                <el-form-item label="实际车辆" prop="assignMaintain.cars">
                                    <CarSelector v-model="form.assignMaintain.cars" placeholder="请选择实际车辆" clearable multiple />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="gutter">
                            <el-col :span="12">
                                <el-form-item label="实际耗费物资" prop="assignMaintain.resource">
                                    <!-- 动态物资列表 -->
                                    <div class="resource-list">
                                        <div v-for="(resource, index) in actualResourceList" :key="index" class="resource-item">
                                            <!-- 物资选择 -->
                                            <el-select
                                                v-model="resource.resourceId"
                                                placeholder="请选择物资"
                                                clearable
                                                style="width: 200px; margin-right: 10px"
                                                @change="updateResourceField"
                                            >
                                                <el-option
                                                    v-for="resourceType in resourceTypes"
                                                    :key="resourceType.id"
                                                    :label="resourceType.name"
                                                    :value="resourceType.id"
                                                />
                                            </el-select>

                                            <!-- 数量输入 -->
                                            <el-input-number v-model="resource.quantity" :min="1" placeholder="数量" @change="updateResourceField" />

                                            <!-- 单位显示 -->
                                            <span class="unit-display">
                                                {{ getResourceTypeInfo(resource.resourceId).unit }}
                                            </span>

                                            <!-- 删除按钮 -->
                                            <el-button
                                                type="danger"
                                                size="small"
                                                @click="removeActualResourceItem(index)"
                                                :disabled="actualResourceList.length <= 1"
                                            >
                                                删除
                                            </el-button>
                                        </div>

                                        <!-- 添加物资按钮 -->
                                        <el-button type="primary" size="small" @click="addActualResourceItem" class="add-resource-btn" plain>
                                            + 添加物资
                                        </el-button>
                                    </div>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="gutter">
                            <el-col :span="6">
                                <el-form-item label="实际时长" prop="assignMaintain.duration">
                                    <el-input-number
                                        v-model="form.assignMaintain.duration"
                                        :min="0.1"
                                        :precision="1"
                                        :step="0.1"
                                        placeholder="请输入实际时长（小时）"
                                        style="width: 100%"
                                    ></el-input-number>
                                    <div style="font-size: 12px; color: #909399; margin-top: 4px">选择开始和结束时间后将自动计算，也可手动调整</div>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="gutter">
                            <el-col :span="4">
                                <el-form-item label="执行相关图片" prop="images">
                                    <imageUpload v-model="form.assignMaintain.images" @update:modelValue="handleExecuteUploadChange" />
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>

                    <!-- 审批意见 -->
                    <!-- 某些环节不需要审批意见，用v-show控制，确认：confirm、二次验收：second_acceptance、待确认取消：pending_cancellation-->
                    <div
                        class="auditOption"
                        v-show="
                            form.task.currentStatus == 'Confirm' ||
                            form.task.currentStatus == 'Second_Acceptance' ||
                            form.task.currentStatus == 'Pending_Cancellation'
                        "
                    >
                        <el-row :gutter="gutter">
                            <el-col :span="12">
                                <el-form-item :label="form.task.currentStatus == 'Confirm' ? '是否确认' : '验收意见'">
                                    <el-radio-group v-model="form.nextAssignee.wfOperation">
                                        <el-radio v-for="option in filteredApprovalOptions" :key="option.value" :value="option.value">
                                            {{ option.label }}
                                        </el-radio>
                                    </el-radio-group>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="gutter">
                            <el-col :span="12">
                                <el-form-item label="意见">
                                    <el-input v-model="form.nextAssignee.opinion" type="textarea" placeholder="请输入内容" />
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                    <!-- 当form.nextAssignee.businessData.CANCEL_OPTION == 'CANCEL'时，后端会启动取消流程
                    @todo 这里的表单要做控制
                    -->
                    <!-- <div>
                        <el-form-item label="走取消流程分支">
                            <el-input v-model="form.nextAssignee.businessData.CANCEL_OPTION" type="textarea"
                                placeholder="请输入内容" />
                        </el-form-item>
                    </div> -->
                    <div class="text item">
                        <el-row justify="center">
                            <el-col style="text-align: center">
                                <!-- START状态：显示提交和变更计划日期按钮 -->
                                <template v-if="form.task.currentStatus == 'START'">
                                    <el-button type="primary" :loading="submitLoading" @click="handleSubmit" style="margin-right: 12px"
                                        >提交</el-button
                                    >
                                </template>
                                <!-- 其他状态：仅显示提交按钮 -->

                                <template v-else>
                                    <el-button
                                        type="primary"
                                        :disabled="form.task.currentStatus == 'Execute' && form.expired"
                                        :loading="submitLoading"
                                        @click="handleSubmit"
                                        >提交</el-button
                                    >
                                </template>
                                <el-button
                                    v-if="form.task.currentStatus == 'Execute'"
                                    type="primary"
                                    :loading="submitLoading"
                                    @click="handleChangePlanDate"
                                    >变更计划日期</el-button
                                >
                            </el-col>
                        </el-row>
                    </div>
                </el-form>
            </div>
        </el-card>
        <el-card class="box-card">
            <template v-slot:header>
                <div class="clearfix">
                    <span>审批进度</span>
                </div>
            </template>
            <WorkflowInfo :business-id="taskId" exclude-activity-codes="START,Confirm" />
        </el-card>
        <!-- 变更计划日期组件 -->
        <ChangeTaskDate
            v-model="changePlanDateVisible"
            :project-id="appStore.projectContext.selectedProjectId"
            :task-id="taskId"
            :current-date="form.task.bgnDate"
            taskType="curing"
            @success="handleChangePlanDateSuccess"
            @navigate-to-list="handleNavigateToList"
        />
    </div>
</template>
<script setup lang="ts">
import { ref, reactive, getCurrentInstance, toRefs, onMounted, computed } from 'vue'
import { useAppStore } from '@/store/modules/app'
import { useRoute, useRouter } from 'vue-router'

import { maintainAssign } from '@/api/plan/task'
import { AssignMaintainFlowForm, AssignMaintainFlowVo, AssignMaintainResourceVo } from '@/api/plan/assignMaintain/types'
import { getTaskAssignmentByTaskId } from '@/api/plan/assignMaintain'
import { ElMessage } from 'element-plus'
import BaseInfoMaintain from '../../components/BaseInfoMaintain.vue'
import WorkflowInfo from '../../components/Workflow/WorkflowInfo.vue'
import ChangeTaskDate from '../../components/ChangeTaskDate.vue'
import TeamSelector from '../../components/TeamSelector/index.vue'
import ManagersSelector from '../../components/ManagersSelector/index.vue'
import CarSelector from '../../components/CarSelector/index.vue'
import TaskDelayHistory from '../../components/TaskDelayHistory.vue'
import { listResourceType } from '@/api/common/resourceType'
import { ResourceTypeVO } from '@/api/common/resourceType/types'

const { proxy } = getCurrentInstance() as ComponentInternalInstance
const appStore = useAppStore()
const route = useRoute()
const router = useRouter()
const { job_type, tnl_resource_unit } = toRefs<any>(proxy?.useDict('job_type', 'tnl_resource_unit'))

const gutter = ref(50) //设置项目表单两列的距离
const taskId = ref((route.query.taskId as string) || '')
const projectFormRef = ref()
const submitLoading = ref(false)
const resourceTypes = ref<ResourceTypeVO[]>([])
const actualResourceList = ref<AssignMaintainResourceVo[]>([
    {
        resourceId: '',
        quantity: 1
    } as AssignMaintainResourceVo
])

// 变更计划日期相关数据
const changePlanDateVisible = ref(false)

// 审核结果选项
const approvalOptions = ref([
    { label: '通过', value: 'APPROVE' },
    { label: '退回', value: 'ROLLBACK' }
    // { label: '延期', value: 'SUSPENDED' }
])

// 过滤后的审核选项 - 根据当前状态动态显示
const filteredApprovalOptions = computed(() => {
    // 如果当前状态是 Second_Acceptance，则不显示 SUSPENDED 选项
    if (form.task.currentStatus === 'Second_Acceptance') {
        return approvalOptions.value.filter((option) => option.value !== 'SUSPENDED')
    }
    return approvalOptions.value
})

// 表单数据 - 提供完整的初始化
const form = reactive<AssignMaintainFlowForm>({
    task: {
        tempTask: 'no',
        tempResourceId: ''
    },
    expired: false,
    assignMaintain: {
        taskId: taskId.value,
        resourceList: [
            {
                resourceId: '',
                quantity: 1
            } as AssignMaintainResourceVo
        ],
        cars: [] as any // 初始化为空数组，支持多选
    },
    nextAssignee: {
        nextAssignees: undefined, // 单选模式，初始化为undefined
        wfOperation: 'APPROVE', // 添加审批状态字段
        businessData: {} // 初始化businessData对象
    }
})

// 作业结束时间的日期限制函数
const disabledEndDate = (time: Date) => {
    // 如果没有选择作业开始时间，则不限制
    if (!form.task.bgnDate) {
        return false
    }

    // 将开始时间字符串转换为Date对象
    const startDate = new Date(form.task.bgnDate)

    // 获取开始时间的日期部分（不包含时间）
    const startDateOnly = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate())

    // 获取开始时间后一天的日期部分
    const nextDateOnly = new Date(startDateOnly)
    nextDateOnly.setDate(nextDateOnly.getDate() + 1)

    // 获取当前选择日期的日期部分（不包含时间）
    const currentDateOnly = new Date(time.getFullYear(), time.getMonth(), time.getDate())

    // 只允许选择开始时间当天或后一天
    return currentDateOnly.getTime() !== startDateOnly.getTime() && currentDateOnly.getTime() !== nextDateOnly.getTime()
}

// 作业开始时间变化处理函数
const handleStartDateChange = (value: string | null) => {
    if (!value) {
        return
    }

    // 检查结束时间是否需要重新选择
    if (form.task.endDate) {
        const startDate = new Date(value)
        const endDate = new Date(form.task.endDate)

        // 获取日期部分（不包含时间）
        const startDateOnly = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate())
        const endDateOnly = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate())

        // 获取开始时间后一天的日期部分
        const nextDateOnly = new Date(startDateOnly)
        nextDateOnly.setDate(nextDateOnly.getDate() + 1)

        // 检查结束时间是否在允许的范围内（当天或后一天）
        const isValidEndDate = endDateOnly.getTime() === startDateOnly.getTime() || endDateOnly.getTime() === nextDateOnly.getTime()

        if (!isValidEndDate) {
            ElMessage.warning('作业开始时间已变更，请重新选择作业结束时间（可选择当天或后一天）')
            form.task.endDate = ''
            // 清空实际时长
            form.assignMaintain.duration = null
        } else if (endDateOnly.getTime() === startDateOnly.getTime() && endDate.getTime() < startDate.getTime()) {
            // 如果是同一天但结束时间早于开始时间，也需要重新选择
            ElMessage.warning('作业开始时间已变更，请重新选择作业结束时间（同一天内不能早于开始时间）')
            form.task.endDate = ''
            // 清空实际时长
            form.assignMaintain.duration = null
        } else {
            // 开始时间变更但结束时间仍然有效，重新计算时长
            calculateDuration()
        }
    }
}

// 计算实际时长（精确到小数点后一位）
const calculateDuration = () => {
    if (!form.task.bgnDate || !form.task.endDate) {
        return
    }

    const startDate = new Date(form.task.bgnDate)
    const endDate = new Date(form.task.endDate)

    // 计算时间差（毫秒）
    const timeDiff = endDate.getTime() - startDate.getTime()

    // 转换为小时，精确到小数点后一位
    const hours = Math.round((timeDiff / (1000 * 60 * 60)) * 10) / 10

    // 更新实际时长
    form.assignMaintain.duration = hours

    return hours
}

// 作业结束时间变化处理函数
const handleEndDateChange = (value: string | null) => {
    if (!value) {
        return
    }

    // 检查是否选择了开始时间
    if (!form.task.bgnDate) {
        ElMessage.warning('请先选择作业开始时间')
        form.task.endDate = ''
        return
    }

    const startDate = new Date(form.task.bgnDate)
    const endDate = new Date(value)

    // 获取日期部分（不包含时间）
    const startDateOnly = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate())
    const endDateOnly = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate())

    // 获取开始时间后一天的日期部分
    const nextDateOnly = new Date(startDateOnly)
    nextDateOnly.setDate(nextDateOnly.getDate() + 1)

    // 检查结束时间是否在允许的范围内（当天或后一天）
    const isValidEndDate = endDateOnly.getTime() === startDateOnly.getTime() || endDateOnly.getTime() === nextDateOnly.getTime()

    if (!isValidEndDate) {
        ElMessage.error('作业结束时间只能选择开始时间当天或后一天')
        form.task.endDate = ''
        return
    }

    // 如果是同一天，检查结束时间是否早于开始时间
    if (endDateOnly.getTime() === startDateOnly.getTime() && endDate.getTime() < startDate.getTime()) {
        ElMessage.error('同一天内作业结束时间不能早于作业开始时间')
        form.task.endDate = ''
        return
    }

    // 验证通过，计算并更新实际时长
    const calculatedHours = calculateDuration()

    // 显示成功提示
    const timeDiff = endDate.getTime() - startDate.getTime()
    const hours = Math.floor(timeDiff / (1000 * 60 * 60))
    const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60))

    // 判断是否跨天
    const isCrossDay = endDateOnly.getTime() !== startDateOnly.getTime()
    const dayText = isCrossDay ? '（跨天作业）' : ''

    ElMessage.success(`作业时长：${hours}小时${minutes}分钟${dayText}，实际时长已自动设置为：${calculatedHours}小时`)
}

// 计算实际时长（基于实际开工日期和实际结束日期）
const calculateActualDuration = () => {
    if (!form.task.taskStartDate || !form.task.taskFinishDate) {
        console.log('实际时长计算跳过: 缺少实际开工日期或实际结束日期')
        return
    }

    const startDate = new Date(form.task.taskStartDate)
    const endDate = new Date(form.task.taskFinishDate)

    // 验证日期有效性
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        console.error('实际时长计算失败: 日期格式无效')
        return
    }

    // 计算时间差（毫秒）
    const timeDiff = endDate.getTime() - startDate.getTime()

    if (timeDiff < 0) {
        console.warn('实际时长计算警告: 结束时间早于开始时间')
        return
    }

    // 转换为小时，精确到小数点后一位
    const hours = Math.round((timeDiff / (1000 * 60 * 60)) * 10) / 10

    // 更新实际时长到duration字段
    form.assignMaintain.duration = hours

    console.log(`实际时长计算: ${form.task.taskStartDate} 到 ${form.task.taskFinishDate} = ${hours}小时`)

    return hours
}

// 实际开工日期变化处理函数
const handleActualStartDateChange = (value: string | null) => {
    if (!value) {
        return
    }

    console.log('实际开工日期变化:', value)

    // 如果实际结束日期也已选择，则计算实际时长
    if (form.task.taskFinishDate) {
        const startDate = new Date(value)
        const endDate = new Date(form.task.taskFinishDate)

        // 检查结束时间是否早于开始时间
        if (endDate.getTime() < startDate.getTime()) {
            ElMessage.warning('实际结束日期不能早于实际开工日期，请重新选择')
            form.task.taskFinishDate = ''
            form.assignMaintain.duration = null
        } else {
            calculateActualDuration()
        }
    }
}

// 实际结束日期变化处理函数
const handleActualEndDateChange = (value: string | null) => {
    if (!value) {
        return
    }

    console.log('实际结束日期变化:', value)

    // 检查是否选择了实际开工日期
    if (!form.task.taskStartDate) {
        ElMessage.warning('请先选择实际开工日期')
        form.task.taskFinishDate = ''
        return
    }

    const startDate = new Date(form.task.taskStartDate)
    const endDate = new Date(value)

    // 检查结束时间是否早于开始时间
    if (endDate.getTime() < startDate.getTime()) {
        ElMessage.error('实际结束日期不能早于实际开工日期')
        form.task.taskFinishDate = ''
        return
    }

    // 验证通过，计算并更新实际时长
    const calculatedHours = calculateActualDuration()

    // 显示成功提示
    const timeDiff = endDate.getTime() - startDate.getTime()
    const hours = Math.floor(timeDiff / (1000 * 60 * 60))
    const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60))
    const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24))

    let timeText = ''
    if (days > 0) {
        timeText = `${days}天${hours % 24}小时${minutes}分钟`
    } else {
        timeText = `${hours}小时${minutes}分钟`
    }

    ElMessage.success(`实际作业时长：${timeText}，已自动设置为：${calculatedHours}小时`)
}

// 表单验证规则 - 更新prop路径
const rules = reactive({
    'task.bgnDate': [{ required: true, message: '请选择作业开始时间', trigger: 'change' }],
    'task.endDate': [{ required: true, message: '请选择作业结束时间', trigger: 'change' }],
    'assignMaintain.jobType': [{ required: true, message: '请选择作业类型', trigger: 'change' }],
    'assignMaintain.safeManId': [{ required: true, message: '请选择安全员', trigger: 'change' }],
    'nextAssignee.nextAssignees': [{ required: true, message: '请选择带班负责人', trigger: 'change' }],
    'assignMaintain.duration': [{ required: true, message: '请输入执行时长', trigger: 'blur' }],
    'nextAssignee.approvalStatus': [{ required: true, message: '请选择状态', trigger: 'change' }],
    // Execute状态下的必填字段
    'task.taskStartDate': [
        {
            required: true,
            message: '请选择实际开工日期',
            trigger: 'change',
            validator: (_rule: any, value: any, callback: any) => {
                if (form.task.currentStatus === 'Execute' && !value) {
                    callback(new Error('请选择实际开工日期'))
                } else {
                    callback()
                }
            }
        }
    ],
    'task.taskFinishDate': [
        {
            required: true,
            message: '请选择实际结束日期',
            trigger: 'change',
            validator: (_rule: any, value: any, callback: any) => {
                if (form.task.currentStatus === 'Execute' && !value) {
                    callback(new Error('请选择实际结束日期'))
                } else {
                    callback()
                }
            }
        }
    ],
    'assignMaintain.teams': [
        {
            required: true,
            message: '请选择实际班组',
            trigger: 'change',
            validator: (_rule: any, value: any, callback: any) => {
                if (form.task.currentStatus === 'Execute' && (!value || value.length === 0)) {
                    callback(new Error('请选择实际班组'))
                } else {
                    callback()
                }
            }
        }
    ],
    'assignMaintain.cars': [
        {
            required: true,
            message: '请选择实际车辆',
            trigger: 'change',
            validator: (_rule: any, value: any, callback: any) => {
                if (form.task.currentStatus === 'Execute' && (!value || value.length === 0)) {
                    callback(new Error('请选择实际车辆'))
                } else {
                    callback()
                }
            }
        }
    ]
})

// 获取所有物资类别
const getResourceTypes = async () => {
    try {
        console.log('开始获取物资类别列表...')
        const queryParams = {
            pageNum: 1,
            pageSize: 100000
        }

        const res = await listResourceType(queryParams)
        console.log('API返回的物资类型数据:', res)

        // 兼容不同的API返回结构
        resourceTypes.value = Array.isArray(res) ? res : res.rows || res.data || []
        console.log('解析后的物资类型列表:', resourceTypes.value)

        if (resourceTypes.value.length === 0) {
            console.warn('没有获取到物资类型数据')
        } else {
            console.log(`成功获取 ${resourceTypes.value.length} 个物资类型`)
        }
    } catch (error) {
        console.error('获取物资类型列表失败:', error)
        resourceTypes.value = []
        ElMessage.warning('获取物资类型列表失败')
    }
}

// 获取任务分配数据
const getTaskAssignmentData = async () => {
    try {
        const response = await getTaskAssignmentByTaskId(taskId.value)
        if (response.data) {
            const data: AssignMaintainFlowVo = response.data

            // 初始化form数据
            if (data.task) {
                Object.assign(form.task, data.task)
                form.expired = data.expired
                if (data.task.currentStatus == 'Execute') {
                    form.task.taskStartDate = form.task.bgnDate
                    form.task.taskFinishDate = form.task.endDate

                    // 页面加载时自动计算实际时长
                    setTimeout(() => {
                        calculateActualDuration()
                    }, 100) // 延迟一点确保数据完全加载
                }
                console.log('任务信息:', {
                    id: data.task.id,
                    name: data.task.name,
                    taskType: data.task.taskType,
                    bgnDate: data.task.bgnDate,
                    endDate: data.task.endDate,
                    taskStartDate: form.task.taskStartDate,
                    taskFinishDate: form.task.taskFinishDate
                })
            }

            if (data.assignMaintain) {
                Object.assign(form.assignMaintain, data.assignMaintain)

                // 如果从数据库加载了已有的实际日期（存储在task中），也要计算实际时长
                if (data.task?.currentStatus === 'Execute' && form.task.taskStartDate && form.task.taskFinishDate) {
                    setTimeout(() => {
                        calculateActualDuration()
                    }, 150) // 稍微延迟确保所有数据都已加载
                }

                // 解析cars字段（逗号分隔的字符串转为数组）
                if (data.assignMaintain.cars && typeof data.assignMaintain.cars === 'string') {
                    ;(form.assignMaintain.cars as any) = data.assignMaintain.cars.split(',').filter((id) => id.trim() !== '')
                    console.log('解析车辆ID列表成功:', form.assignMaintain.cars)
                } else if (Array.isArray(data.assignMaintain.cars)) {
                    // 如果已经是数组，直接使用
                    ;(form.assignMaintain.cars as any) = data.assignMaintain.cars
                } else {
                    // 初始化为空数组
                    ;(form.assignMaintain.cars as any) = []
                }

                // 解析resource字段为actualResourceList
                if (data.assignMaintain.resource) {
                    try {
                        const parsedResource = JSON.parse(data.assignMaintain.resource)
                        if (Array.isArray(parsedResource)) {
                            actualResourceList.value = parsedResource
                            console.log('解析实际物资列表成功:', actualResourceList.value)
                        }
                    } catch (error) {
                        console.warn('解析resource字段失败，使用默认值:', error)
                        actualResourceList.value = [
                            {
                                resourceId: '',
                                quantity: 1
                            } as AssignMaintainResourceVo
                        ]
                    }
                } else {
                    // 如果没有resource数据，初始化为默认值
                    actualResourceList.value = [
                        {
                            resourceId: '',
                            quantity: 1
                        } as AssignMaintainResourceVo
                    ]
                }

                console.log('维养分配信息:', {
                    id: data.assignMaintain.id,
                    taskId: data.assignMaintain.taskId,
                    name: data.assignMaintain.name,
                    jobType: data.assignMaintain.jobType,
                    safeManId: data.assignMaintain.safeManId,
                    managerId: data.assignMaintain.managerId,
                    teams: data.assignMaintain.teams,
                    cars: data.assignMaintain.cars,
                    duration: data.assignMaintain.duration,
                    description: data.assignMaintain.description
                })
            }

            console.log('获取任务分配数据成功:', data)
        }
    } catch (error) {
        console.error('获取任务分配数据失败:', error)
        ElMessage.warning('获取任务数据失败，将使用默认数据')
    }
}

// 提交表单
const handleSubmit = async () => {
    try {
        // 1.表单验证
        debugger
        const valid = await projectFormRef.value?.validate()
        if (!valid) {
            ElMessage.warning('请输入必填项')
            return
        }

        submitLoading.value = true

        // 在提交前确保实际物资数据已同步到resource字段
        if (form.task.currentStatus !== 'START') {
            updateResourceField()
        }

        const processedForm = {
            ...form
        }

        // 2.处理车辆和资源列表数据
        if (processedForm.assignMaintain) {
            // 处理车辆数据：将数组转换为逗号分隔的字符串
            if (Array.isArray(processedForm.assignMaintain.cars)) {
                processedForm.assignMaintain.cars = (processedForm.assignMaintain.cars as string[]).join(',')
                console.log('转换车辆数组为逗号分隔字符串:', processedForm.assignMaintain.cars)
            }
            // 根据当前状态决定如何处理物资数据
            if (form.task.currentStatus === 'START') {
                // 指派环节：保存估算物资到resource字段
                if (processedForm.assignMaintain.resourceList && processedForm.assignMaintain.resourceList.length > 0) {
                    const validResources = processedForm.assignMaintain.resourceList.filter(
                        (resource) => resource.resourceId && resource.quantity > 0
                    )
                    if (validResources.length > 0) {
                        processedForm.assignMaintain.resource = JSON.stringify(validResources)
                        console.log('转换估算物资resourceList为JSON:', processedForm.assignMaintain.resource)
                    }
                }
            } else {
                // 执行环节及其他环节：保存实际物资到resource字段
                if (actualResourceList.value && actualResourceList.value.length > 0) {
                    // 过滤掉空的物资项
                    const validResources = actualResourceList.value.filter((resource) => resource.resourceId && resource.quantity > 0)
                    if (validResources.length > 0) {
                        processedForm.assignMaintain.resource = JSON.stringify(validResources)
                        console.log('转换实际物资actualResourceList为JSON:', processedForm.assignMaintain.resource)
                    }
                } else {
                    // 如果没有实际物资数据，保持原有的resource字段不变
                    console.log('没有实际物资数据，保持原有resource字段:', processedForm.assignMaintain.resource)
                }
            }
        }

        // 3.根据当前状态处理nextAssignee
        switch (form.task.currentStatus) {
            // 指派环节需要指定处理人
            case 'START':
                processedForm.nextAssignee = {
                    ...form.nextAssignee,
                    nextAssignees: form.nextAssignee?.nextAssignees
                        ? Array.isArray(form.nextAssignee.nextAssignees)
                            ? form.nextAssignee.nextAssignees
                            : [form.nextAssignee.nextAssignees]
                        : []
                }
                break
            default:
                // 其他环节不需要指定处理人
                processedForm.nextAssignee = {
                    ...form.nextAssignee,
                    nextAssignees: []
                }
                break
        }

        console.log('提交的维养任务指派数据:', processedForm)
        console.log('nextAssignees转换后:', processedForm.nextAssignee?.nextAssignees)

        // 4.调用提交后端更新实体和流程审批
        await maintainAssign(processedForm)

        ElMessage.success('维养任务指派成功')

        // router.push('/subProject/maintain/list');
    } catch (error) {
        console.error('维养任务指派失败:', error)
        ElMessage.error('维养任务指派失败，请重试')
    } finally {
        submitLoading.value = false
    }
    router.push('/subProject/maintain/list')
}

/** 处理安全交底图片上传变化 */
const handleSafeUploadChange = (ossIds: string) => {
    form.assignMaintain.safeImages = ossIds
}

//执行阶段图片上传变化
const handleExecuteUploadChange = (ossIds: string) => {
    form.assignMaintain.images = ossIds
}

// 变更计划日期相关方法
// 打开变更计划日期对话框
const handleChangePlanDate = () => {
    changePlanDateVisible.value = true
}

// 处理变更计划日期成功
const handleChangePlanDateSuccess = (newDate: string) => {
    // 更新本地数据
    form.task.bgnDate = newDate
    console.log('计划日期已更新为:', newDate)
}

// 处理跳转到列表页面
const handleNavigateToList = () => {
    router.push('/subProject/maintain/list')
}

// 添加物资项
const addResourceItem = () => {
    if (!form.assignMaintain.resourceList) {
        form.assignMaintain.resourceList = []
    }
    form.assignMaintain.resourceList.push({
        resourceId: '',
        quantity: 1
    } as AssignMaintainResourceVo)
}

// 删除物资项
const removeResourceItem = (index: number) => {
    if (form.assignMaintain.resourceList && form.assignMaintain.resourceList.length > 1) {
        form.assignMaintain.resourceList.splice(index, 1)
    }
}

// 获取物资类型名称和单位
const getResourceTypeInfo = (resourceId: string) => {
    const resourceType = resourceTypes.value.find((rt) => rt.id === resourceId)
    const unitDict = tnl_resource_unit.value?.find((dict) => dict.value === resourceType?.unit)
    return {
        name: resourceType?.name || '',
        unit: unitDict?.label || resourceType?.unit || ''
    }
}

// 添加实际物资项
const addActualResourceItem = () => {
    actualResourceList.value.push({
        resourceId: '',
        quantity: 1
    } as AssignMaintainResourceVo)
    updateResourceField()
}

// 删除实际物资项
const removeActualResourceItem = (index: number) => {
    if (actualResourceList.value.length > 1) {
        actualResourceList.value.splice(index, 1)
        updateResourceField()
    }
}

// 更新resource字段
const updateResourceField = () => {
    // 过滤掉空的物资项
    const validResources = actualResourceList.value.filter((resource) => resource.resourceId && resource.quantity > 0)
    if (validResources.length > 0) {
        form.assignMaintain.resource = JSON.stringify(validResources)
    } else {
        form.assignMaintain.resource = ''
    }
}

onMounted(async () => {
    await getResourceTypes()
    await getTaskAssignmentData()
})
</script>

<style lang="scss" scoped>
.box-card {
    margin-top: 10px;
}

.button-center {
    text-align: center;

    .el-button {
        margin: 0 8px;
    }
}

.resource-list {
    .resource-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        gap: 12px;

        .el-select {
            flex: 1;
            max-width: 200px;
        }

        .el-input-number {
            width: 120px;
        }

        .unit-display {
            color: #606266;
            font-size: 14px;
            min-width: 40px;
        }

        .el-button--danger {
            margin-left: auto;
        }
    }

    .add-resource-btn {
        margin-top: 8px;
        border-style: dashed;

        &:hover {
            border-color: #409eff;
            color: #409eff;
        }
    }
}
</style>
