export interface InspectionByDeviceViewVO {
    /**
     * 主键ID
     */
    id: string | number

    /**
     * 项目编号
     */
    projectId: string | number

    /**
     * 管理单元-id
     */
    unitId: string | number

    /**
     * 管理单元名称
     */
    unitName: string

    /**
     * 房间ID
     */
    roomId: string | number

    /**
     * 房间名称
     */
    roomName: string

    /**
     * 房间编码
     */
    roomCode: string

    /**
     * 设施设备名称
     */
    name: string
    remark: string

    /**
     * 设施设备编码
     */
    code: string

    codeType: string

    /**
     * 设施设备分类ID
     */
    categoryIdThird: string | number

    /**
     * 分类路径
     */
    categoryPath: string

    /**
     * 起始里程（km）
     */
    bgnKilometer: string
    endKilometer: string

    /**
     * 设备类型：equipment:设备；facility:设施
     */
    kind: string

    /**
     * 专业类型
     */
    specialty: string
    modelId: string
    brand: string
}

export interface InspectionByDeviceViewForm extends BaseEntity {}

export interface InspectionByDeviceViewVOQuery extends PageQuery {
    /**
     * 项目编号
     */
    projectId: string

    /**
     * 管理单元-id
     */
    unitId?: string | number

    /**
     * 房间ID
     */
    roomId?: string | number

    /**
     * 设施设备名称
     */
    name?: string

    /**
     * 设施设备编码
     */
    code?: string

    /**
     * 备注名称
     */
    remark?: string

    /**
     * 分类路径
     */
    categoryPath?: string

    /**
     * 设备类型：equipment:设备；facility:设施
     */
    kind?: string

    /**
     * 专业类型
     */
    specialty?: string

    /**
     * 日期范围参数
     */
    params?: any
}
