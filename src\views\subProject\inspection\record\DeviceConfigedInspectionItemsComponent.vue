<template>
    <div class="device-inspection-config p-2">
        <!-- 返回按钮区域 -->
        <div class="header-actions mb-4">
            <el-button type="primary" icon="ArrowLeft" @click="handleBack"> 返回设备列表 </el-button>
        </div>

        <!-- 设备信息卡片 -->
        <el-card shadow="never" class="mb-4" v-if="deviceInfo">
            <div class="device-info-header">
                <h2 class="device-title">{{ deviceInfo.remark || deviceInfo.name }}</h2>
                <div class="device-details">
                    <el-tag type="primary" class="mr-2">{{ deviceInfo.firstLevelCategory || '机电分系统' }}</el-tag>
                    <el-tag type="success" class="mr-2">{{ deviceInfo.secondLevelCategory || '机电子系统' }}</el-tag>
                    <el-tag type="info">{{ deviceInfo.thirdLevelCategory || '设备类型' }}</el-tag>
                </div>
                <div class="device-meta">
                    <span class="meta-item">设备编码: {{ deviceInfo.code || '-' }}</span>
                    <span class="meta-item">管理单元: {{ deviceInfo.unitName || '-' }}</span>
                    <span class="meta-item">房间: {{ deviceInfo.roomName || '-' }}</span>
                </div>
            </div>
        </el-card>

        <!-- 搜索条件区域 -->
        <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
            <div v-show="showSearch" class="mb-[10px]">
                <el-card shadow="hover">
                    <el-form ref="queryFormRef" :model="queryParams" :inline="true">
                        <el-form-item label="巡检类型" prop="inspectType">
                            <el-select
                                v-model="queryParams.inspectType"
                                placeholder="请选择巡检类型"
                                clearable
                                @change="handleQuery"
                                style="width: 200px"
                            >
                                <el-option v-for="dict in inspect_type" :key="dict.value" :label="dict.label" :value="dict.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="配置名称" prop="configName">
                            <el-input
                                v-model="queryParams.configName"
                                placeholder="请输入配置名称"
                                clearable
                                @keyup.enter="handleQuery"
                                style="width: 200px"
                            />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                        </el-form-item>
                    </el-form>
                </el-card>
            </div>
        </transition>

        <!-- 巡检配置项列表 -->
        <el-card shadow="never">
            <template #header>
                <el-row :gutter="10" class="mb8">
                    <el-col :span="1.5">
                        <span class="card-title">设备巡检配置项</span>
                    </el-col>
                    <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
                </el-row>
            </template>

            <el-table v-loading="loading" :data="inspectionConfigItemList" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="序号" type="index" width="60" align="center" />
                <el-table-column label="巡检项名称" align="center" prop="configName" show-overflow-tooltip />
                <el-table-column label="巡检类型" align="center" prop="inspectType" width="120">
                    <template #default="scope">
                        {{ getInspectTypeLabel(scope.row.inspectType) }}
                    </template>
                </el-table-column>
                <el-table-column label="检查标准" align="center" prop="standard" show-overflow-tooltip />
                <el-table-column label="预警值" align="center" show-overflow-tooltip>
                    <template #default="scope">
                        {{ scope.row.warningMin + ' ~ ' + scope.row.warningMax }}
                    </template>
                </el-table-column>
                <el-table-column label="告警值" align="center" show-overflow-tooltip>
                    <template #default="scope">
                        {{ scope.row.alarmMin + ' ~ ' + scope.row.alarmMax }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
                    <template #default="scope">
                        <el-button link type="primary" @click="handleViewRecords(scope.row)"> 查看巡检记录 </el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 参考 configItems.vue，移除分页组件 -->
            <!-- <pagination
                v-show="total > 0"
                :total="total"
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                @pagination="getList"
            /> -->
        </el-card>

        <!-- 巡检历史记录抽屉 -->
        <InspectionHistory
            v-model:visible="showInspectionHistory"
            :device-info="deviceInfo"
            :config-item="selectedConfigItem"
            @close="handleCloseHistory"
        />
    </div>
</template>

<script setup name="DeviceConfigedInspectionItemsComponent" lang="ts">
import { ref, reactive, onMounted, getCurrentInstance, ComponentInternalInstance, toRefs, computed, watch } from 'vue'
import { ElMessage, FormInstance } from 'element-plus'
import { useAppStore } from '@/store/modules/app'
import { getConfigItems } from '@/api/subProject/inspection/inspectionConfigItem'
import { InspectionConfigItemQuery, InspectionConfigItemViewVO } from '@/api/subProject/inspection/inspectionConfigItem/types'
import { InspectionByDeviceViewVO } from '@/api/subProject/inspection/inspectionByDeviceView/types'
import InspectionHistory from './InspectionHistory.vue'

const { proxy } = getCurrentInstance() as ComponentInternalInstance
const appStore = useAppStore()

// 获取数据字典
const { inspect_type, inspect_unit } = toRefs<any>(proxy?.useDict('inspect_type', 'inspect_unit'))

// 定义 props
interface Props {
    deviceInfo: InspectionByDeviceViewVO
}

const props = defineProps<Props>()

// 定义 emits
const emit = defineEmits(['back', 'view-records'])

// 响应式数据
const inspectionConfigItemList = ref<InspectionConfigItemViewVO[]>([])
const loading = ref(true)
const showSearch = ref(true)
const ids = ref<Array<string | number>>([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)

// 巡检历史记录抽屉状态
const showInspectionHistory = ref(false)
const selectedConfigItem = ref<InspectionConfigItemViewVO | null>(null)

const queryFormRef = ref<FormInstance>()

const queryParams = reactive<InspectionConfigItemQuery>({
    pageNum: 1,
    pageSize: 10,
    projectId: undefined,
    deviceId: undefined,
    inspectType: undefined,
    configName: undefined,
    params: {}
})

/** 调试信息 */
const debugInfo = computed(() => ({
    hasDeviceInfo: !!props.deviceInfo,
    deviceId: props.deviceInfo?.id,
    projectId: appStore.projectContext.selectedProjectId,
    queryParams: queryParams
}))

/** 获取巡检类型标签 */
const getInspectTypeLabel = (value: string) => {
    const item = inspect_type.value?.find((item: any) => item.value === value)
    return item ? item.label : value
}

/** 查询设施设备巡检配置项列表 */
const getList = async () => {
    console.log('开始获取配置项列表，调试信息:', debugInfo.value)

    // 验证必要参数
    if (!props.deviceInfo || !props.deviceInfo.id) {
        console.warn('设备信息不完整，无法获取配置项')
        return
    }

    const projectId = appStore.projectContext.selectedProjectId
    if (!projectId) {
        console.warn('项目ID不存在，无法获取配置项')
        ElMessage.warning('请先选择项目')
        return
    }

    loading.value = true
    try {
        // 设置查询参数，参考 configItems.vue 的实现
        const configQuery = {
            deviceId: props.deviceInfo.id,
            projectId: projectId,
            inspectType: queryParams.inspectType,
            configName: queryParams.configName
            // 注意：参考 configItems.vue，不传递分页参数
        }

        console.log('查询参数:', configQuery)
        const response = await getConfigItems(configQuery)
        console.log('API 响应:', response)

        if (response && response.data) {
            // 参考 configItems.vue 的数据处理方式
            inspectionConfigItemList.value = Array.isArray(response.data) ? response.data : []
            total.value = inspectionConfigItemList.value.length
        } else {
            inspectionConfigItemList.value = []
            total.value = 0
        }
    } catch (error) {
        console.error('获取巡检配置项列表失败:', error)
        const errorMessage = error instanceof Error ? error.message : '未知错误'
        ElMessage.error(`获取巡检配置项列表失败: ${errorMessage}`)
        inspectionConfigItemList.value = []
        total.value = 0
    } finally {
        loading.value = false
    }
}

/** 搜索按钮操作 */
const handleQuery = () => {
    getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
    queryFormRef.value?.resetFields()
    queryParams.inspectType = undefined
    queryParams.configName = undefined
    handleQuery()
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: InspectionConfigItemViewVO[]) => {
    ids.value = selection.map((item) => item.id)
    single.value = selection.length !== 1
    multiple.value = !selection.length
}

/** 返回设备列表 */
const handleBack = () => {
    emit('back')
}

/** 查看巡检记录 */
const handleViewRecords = (row: InspectionConfigItemViewVO) => {
    console.log('查看巡检记录:', row)
    selectedConfigItem.value = row
    showInspectionHistory.value = true
    emit('view-records', row)
}

/** 关闭巡检历史记录抽屉 */
const handleCloseHistory = () => {
    showInspectionHistory.value = false
    selectedConfigItem.value = null
}

/** 监听设备信息变化 */
watch(
    () => props.deviceInfo,
    (newDeviceInfo) => {
        console.log('设备信息变化:', newDeviceInfo)
        if (newDeviceInfo && newDeviceInfo.id) {
            getList()
        }
    },
    { immediate: true }
)

/** 初始化 */
onMounted(() => {
    // 移除直接调用 getList()，改为通过 watch 触发
    console.log('组件已挂载，设备信息:', props.deviceInfo)
})
</script>

<style scoped>
.device-inspection-config {
    height: 100%;
}

.header-actions {
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.device-info-header {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.device-title {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #303133;
}

.device-details {
    display: flex;
    gap: 8px;
    align-items: center;
}

.device-meta {
    display: flex;
    gap: 24px;
    color: #606266;
    font-size: 14px;
}

.meta-item {
    display: inline-block;
}

.card-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
}

.mr-2 {
    margin-right: 8px;
}

.mb-4 {
    margin-bottom: 16px;
}
</style>
