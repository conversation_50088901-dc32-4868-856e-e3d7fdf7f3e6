<!-- 交通流量-flow -->
<template>
    <div class="traffic-flow-container">
        <!-- 数据卡片 -->
        <div class="data-cards">
            <!-- 总流量 -->
            <div class="data-card" v-loading="totalFlowLoading">
                <div class="card-title">总流量</div>
                <div class="card-value">{{ totalFlow }} <span>万</span></div>
                <div class="card-footer">
                    <div class="footer-item">
                        <span>环比</span>
                        <span :class="[ringRatio > 0 ? 'up' : 'down']">{{ ringRatio > 0 ? '+' : '' }}{{ ringRatio }}%</span>
                    </div>
                    <div class="footer-item">
                        <span>同比</span>
                        <span :class="[yearRatio > 0 ? 'up' : 'down']">{{ yearRatio > 0 ? '+' : '' }}{{ yearRatio }}%</span>
                    </div>
                </div>
            </div>

            <!-- 日均 -->
            <div class="data-card" v-loading="dailyFlowLoading">
                <div class="card-title">日均</div>
                <div class="card-value">{{ dailyAverage }} <span>千</span></div>
                <div class="card-footer">
                    <div class="footer-item">
                        <span>环比</span>
                        <span :class="[dailyRingRatio > 0 ? 'up' : 'down']">{{ dailyRingRatio > 0 ? '+' : '' }}{{ dailyRingRatio }}%</span>
                    </div>
                    <div class="footer-item">
                        <span>同比</span>
                        <span :class="[dailyYearRatio > 0 ? 'up' : 'down']">{{ dailyYearRatio > 0 ? '+' : '' }}{{ dailyYearRatio }}%</span>
                    </div>
                </div>
            </div>

            <!-- 高峰时段饱和度 -->
            <div class="data-card" v-loading="saturationLoading">
                <div class="card-title">高峰时段饱和度</div>
                <div class="card-value">{{ peakSaturation }}<span>%</span></div>
            </div>

            <!-- 空白卡片，用于占位 -->
            <!-- <div class="data-card empty"></div> -->
        </div>

        <!-- 图表区域 -->
        <div class="chart-container">
            <!-- 小时交通流量对比图 -->
            <div class="chart-box">
                <div class="chart-header">
                    <div class="chart-title">近24小时交通流量趋势图</div>
                    <div class="chart-selector">
                        <el-select
                            v-model="selectedDetector"
                            placeholder="请选择车检器"
                            @change="updateTrafficChart"
                            :loading="loading24Hour"
                            style="width: 200px"
                        >
                            <el-option v-for="detector in detectorList" :key="detector" :label="detector" :value="detector" />
                        </el-select>
                    </div>
                </div>
                <div id="hourly-traffic-chart" class="chart"></div>
            </div>

            <!-- 流量分析 -->
            <div class="chart-box">
                <div class="chart-header">
                    <div class="chart-title">流量分析</div>
                    <div class="chart-controls">
                        <el-radio-group v-model="flowAnalysisType" size="small">
                            <el-radio-button label="year">年份</el-radio-button>
                            <el-radio-button label="month">月份</el-radio-button>
                            <el-radio-button label="day">日期</el-radio-button>
                            <el-radio-button label="custom">自定义</el-radio-button>
                        </el-radio-group>

                        <!-- 自定义日期范围选择器 -->
                        <el-date-picker
                            v-if="flowAnalysisType === 'custom'"
                            v-model="customDateRange"
                            type="daterange"
                            range-separator="-"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            value-format="YYYY-MM-DD"
                            :disabled-date="disabledDate"
                            style="margin-left: 10px; width: 240px"
                        />
                    </div>
                </div>
                <div id="flow-analysis-chart" class="chart" v-loading="flowAnalysisLoading">
                    <!-- 空数据提示 -->
                    <div v-if="flowAnalysisEmpty && !flowAnalysisLoading" class="empty-data">
                        <el-empty description="暂无数据" />
                    </div>

                    <!-- 错误提示 -->
                    <div v-if="flowAnalysisError && !flowAnalysisLoading" class="error-message">
                        <el-alert :title="flowAnalysisError" type="error" show-icon />
                    </div>
                </div>
            </div>
        </div>
        <el-button type="primary" @click="handleImport" v-hasPermi="['traffic:import']">
            <el-icon><Upload /></el-icon>
            导入车流量数据
        </el-button>
        <el-button type="primary" @click="handleRefresh" v-hasPermi="['traffic:refreshrecent']">
            <el-icon><Upload /></el-icon>
            更新车流量
        </el-button>

        <!-- 导入对话框 -->
        <TrafficImportDialog v-model="importDialogVisible" @import-success="handleImportSuccess" />
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Upload } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import TrafficImportDialog from '@/views/subProject/statistics/trafficOffset/components/TrafficImportDialog.vue'
import {
    getTraffic24Hour,
    refreshTraffic24Hour,
    getTrafficAggregate,
    getTotalFlowStatistics,
    getDailyFlowStatistics,
    getSaturationStatistics
} from '@/api/subProject/health/traffic'
import { useAppStore } from '@/store/modules/app'

// Store实例
const appStore = useAppStore()

// 数据卡片数据
const totalFlow = ref('--')
const ringRatio = ref(0)
const yearRatio = ref(0)
const dailyAverage = ref('--')
const dailyRingRatio = ref(0)
const dailyYearRatio = ref(0)
const peakSaturation = ref('--')

// 数据卡片加载状态
const totalFlowLoading = ref(false)
const dailyFlowLoading = ref(false)
const saturationLoading = ref(false)

// 导入相关数据
const importDialogVisible = ref(false)

// 车检器相关数据
const detectorList = ref<string[]>([]) // 车检器列表
const selectedDetector = ref<string>('') // 选中的车检器
const traffic24HourData = ref<any[]>([]) // 24小时原始数据
const loading24Hour = ref(false) // 加载状态

// 图表选择器数据
const flowAnalysisType = ref('year')

// 流量分析相关数据
const customDateRange = ref<[string, string]>(['', ''])
const flowAnalysisData = ref<any[]>([])
const flowAnalysisLoading = ref(false)
const flowAnalysisError = ref('')
const flowAnalysisEmpty = ref(false)

// 初始化数据
const initData = async () => {
    console.log('开始初始化数据卡片...')

    const projectId = appStore.projectContext.selectedProjectId
    if (!projectId) {
        console.log('项目ID未设置，使用默认数据')
        setDefaultData()
        return
    }

    // 并行加载三个统计数据
    await Promise.all([loadTotalFlowData(projectId), loadDailyFlowData(projectId), loadSaturationData(projectId)])
}

// 设置默认数据
const setDefaultData = () => {
    totalFlow.value = '--'
    ringRatio.value = 0
    yearRatio.value = 0
    dailyAverage.value = '--'
    dailyRingRatio.value = 0
    dailyYearRatio.value = 0
    peakSaturation.value = '--'
}

// 加载总流量数据
const loadTotalFlowData = async (projectId: string) => {
    totalFlowLoading.value = true
    try {
        console.log('加载总流量数据，项目ID:', projectId)
        const response = await getTotalFlowStatistics(projectId)

        if (response.data) {
            const data = response.data
            console.log('总流量数据:', data)

            // 格式化总流量显示（万为单位）
            const flowInWan = data.currentMonthFlow / 10000
            totalFlow.value = flowInWan >= 1 ? flowInWan.toFixed(1) : data.currentMonthFlow.toString()

            // 设置环比和同比
            ringRatio.value = Math.round(data.monthOnMonthGrowth * 10) / 10
            yearRatio.value = Math.round(data.yearOnYearGrowth * 10) / 10

            console.log('总流量数据更新完成:', {
                totalFlow: totalFlow.value,
                ringRatio: ringRatio.value,
                yearRatio: yearRatio.value
            })
        }
    } catch (error) {
        console.error('加载总流量数据失败:', error)
        ElMessage.error('获取总流量数据失败')

        // 设置默认值
        totalFlow.value = '--'
        ringRatio.value = 0
        yearRatio.value = 0
    } finally {
        totalFlowLoading.value = false
    }
}

// 加载当日流量数据
const loadDailyFlowData = async (projectId: string) => {
    dailyFlowLoading.value = true
    try {
        console.log('加载当日流量数据，项目ID:', projectId)
        const response = await getDailyFlowStatistics(projectId)

        if (response.data) {
            const data = response.data
            console.log('当日流量数据:', data)

            // 格式化当日流量显示（千为单位）
            const flowInK = data.currentDayFlow / 1000
            dailyAverage.value = flowInK >= 1 ? flowInK.toFixed(1) : data.currentDayFlow.toString()

            // 设置环比和同比
            dailyRingRatio.value = Math.round(data.dayOnDayGrowth * 10) / 10
            dailyYearRatio.value = Math.round(data.monthOnMonthSameDayGrowth * 10) / 10

            console.log('当日流量数据更新完成:', {
                dailyAverage: dailyAverage.value,
                dailyRingRatio: dailyRingRatio.value,
                dailyYearRatio: dailyYearRatio.value
            })
        }
    } catch (error) {
        console.error('加载当日流量数据失败:', error)
        ElMessage.error('获取当日流量数据失败')

        // 设置默认值
        dailyAverage.value = '--'
        dailyRingRatio.value = 0
        dailyYearRatio.value = 0
    } finally {
        dailyFlowLoading.value = false
    }
}

// 加载饱和度数据
const loadSaturationData = async (projectId: string) => {
    saturationLoading.value = true
    try {
        console.log('加载饱和度数据，项目ID:', projectId)
        const response = await getSaturationStatistics(projectId)

        if (response.data) {
            const data = response.data
            console.log('饱和度数据:', data)

            // 格式化饱和度显示
            peakSaturation.value = data.currentSaturation.toFixed(1)

            console.log('饱和度数据更新完成:', {
                peakSaturation: peakSaturation.value,
                level: data.saturationLevel,
                timeSlot: data.currentTimeSlot
            })
        }
    } catch (error) {
        console.error('加载饱和度数据失败:', error)
        ElMessage.error('获取饱和度数据失败')

        // 设置默认值
        peakSaturation.value = '--'
    } finally {
        saturationLoading.value = false
    }
}

// 加载流量分析数据
const loadFlowAnalysisData = async () => {
    console.log('开始加载流量分析数据...')
    flowAnalysisLoading.value = true
    flowAnalysisError.value = ''
    flowAnalysisEmpty.value = false

    try {
        const projectId = appStore.projectContext.selectedProjectId
        console.log('当前项目ID:', projectId)
        if (!projectId) {
            console.log('项目ID为空，提示用户选择项目')
            ElMessage.error('请先选择项目')
            flowAnalysisLoading.value = false
            return
        }

        // 准备请求参数
        const params: any = {
            type: flowAnalysisType.value,
            projectId: projectId
        }

        // 自定义日期范围处理
        if (flowAnalysisType.value === 'custom') {
            if (customDateRange.value[0] && customDateRange.value[1]) {
                params.startDate = customDateRange.value[0]
                params.endDate = customDateRange.value[1]
            } else {
                console.log('自定义日期范围未选择')
                ElMessage.warning('请选择自定义日期范围')
                flowAnalysisLoading.value = false
                return
            }
        }

        console.log('请求参数:', params)

        // 调用后端接口
        console.log('开始调用getTrafficAggregate接口...')
        const response = await getTrafficAggregate(params)
        console.log('接口响应:', response)

        if (response.data && response.data.length > 0) {
            flowAnalysisData.value = response.data
            updateFlowAnalysisChart(response.data)
            flowAnalysisEmpty.value = false
        } else {
            flowAnalysisData.value = []
            flowAnalysisEmpty.value = true
            updateFlowAnalysisChart([])
            ElMessage.info('暂无数据')
        }
    } catch (error) {
        console.error('获取流量分析数据失败:', error)
        flowAnalysisError.value = '获取数据失败'
        ElMessage.error('获取流量分析数据失败')

        // 清空图表
        flowAnalysisData.value = []
        updateFlowAnalysisChart([])
    } finally {
        flowAnalysisLoading.value = false
    }
}

// 比较timeKey的函数（处理跨年跨月排序问题）
const compareTimeKeys = (timeKeyA: string, timeKeyB: string) => {
    // 解析timeKey为日期对象进行比较
    const parseTimeKey = (timeKey: string) => {
        const parts = timeKey.split('-')

        if (parts.length === 1) {
            // 年份格式: "2024"
            return new Date(parseInt(parts[0]), 0, 1)
        } else if (parts.length === 2) {
            // 月份格式: "2024-01" 或 "2024-1"
            return new Date(parseInt(parts[0]), parseInt(parts[1]) - 1, 1)
        } else if (parts.length === 3) {
            // 日期格式: "2024-01-15" 或 "2024-1-15"
            return new Date(parseInt(parts[0]), parseInt(parts[1]) - 1, parseInt(parts[2]))
        }

        // 无法解析时返回当前时间
        return new Date()
    }

    const dateA = parseTimeKey(timeKeyA)
    const dateB = parseTimeKey(timeKeyB)

    return dateA.getTime() - dateB.getTime()
}

// 处理图表数据
const processChartData = (data: any[]) => {
    if (!data || data.length === 0) {
        return { xAxis: [], data: [] }
    }

    // 智能时间排序：优先使用时间戳，其次使用timeKey解析排序
    const sortedData = [...data].sort((a, b) => {
        // 如果数据包含年月日小时信息，使用时间戳排序
        if (a.year && a.month && a.day !== undefined && a.hour !== undefined && b.year && b.month && b.day !== undefined && b.hour !== undefined) {
            const timeA = new Date(a.year, a.month - 1, a.day, a.hour || 0).getTime()
            const timeB = new Date(b.year, b.month - 1, b.day, b.hour || 0).getTime()
            return timeA - timeB
        }

        // 使用timeKey解析排序（处理跨年跨月问题）
        return compareTimeKeys(a.timeKey, b.timeKey)
    })

    return {
        xAxis: sortedData.map((item) => item.timeKey),
        data: sortedData.map((item) => item.totalFlow || 0)
    }
}

// 格式化X轴标签
const formatXAxisLabel = (value: string, type: string) => {
    switch (type) {
        case 'year':
            return value + '年'
        case 'month':
            return value.split('-')[1] + '月'
        case 'day':
        case 'custom':
            const parts = value.split('-')
            return `${parts[1]}/${parts[2]}`
        default:
            return value
    }
}

// 获取时间标签
const getTimeLabel = (timeKey: string, type: string) => {
    switch (type) {
        case 'year':
            return timeKey + '年'
        case 'month':
            return timeKey.replace('-', '年') + '月'
        case 'day':
        case 'custom':
            return timeKey
                .replace(/-/g, '年')
                .replace(/年(\d+)$/, '年$1日')
                .replace(/年(\d+)年/, '年$1月')
        default:
            return timeKey
    }
}

// 格式化时间标签（用于24小时趋势图）
const formatTimeLabel = (item: any) => {
    const { year, month, day, hour } = item
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const itemDate = new Date(year, month - 1, day)

    // 判断是否为今天
    if (itemDate.getTime() === today.getTime()) {
        return `${hour.toString().padStart(2, '0')}:00`
    }

    // 判断是否为昨天
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
    if (itemDate.getTime() === yesterday.getTime()) {
        return `昨天${hour.toString().padStart(2, '0')}:00`
    }

    // 其他日期显示月日时间
    return `${month}/${day} ${hour.toString().padStart(2, '0')}:00`
}

// 限制自定义日期选择范围
const disabledDate = (time: Date) => {
    const now = new Date()
    const oneYearAgo = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
    return time.getTime() > now.getTime() || time.getTime() < oneYearAgo.getTime()
}

// 更新流量分析图表
const updateFlowAnalysisChart = (data: any[]) => {
    if (!flowChart) return

    // 数据转换
    const chartData = processChartData(data)

    const option = {
        backgroundColor: 'transparent',
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            },
            formatter: (params: any) => {
                if (params && params.length > 0) {
                    const item = params[0]
                    const timeLabel = getTimeLabel(item.axisValue, flowAnalysisType.value)
                    return `${timeLabel}<br/>车流量: ${item.value.toLocaleString()}辆`
                }
                return ''
            },
            backgroundColor: 'rgba(0, 20, 40, 0.8)',
            borderColor: '#4fdbff',
            textStyle: {
                color: '#fff'
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            top: '10px',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: chartData.xAxis,
            axisLine: {
                lineStyle: {
                    color: 'rgba(255, 255, 255, 0.3)'
                }
            },
            axisLabel: {
                color: '#4FDBFF',
                fontSize: 12,
                formatter: (value: string) => formatXAxisLabel(value, flowAnalysisType.value)
            }
        },
        yAxis: {
            type: 'value',
            name: '车流量(辆)',
            nameTextStyle: {
                color: '#4fdbff'
            },
            axisLine: {
                show: true,
                lineStyle: {
                    color: 'rgba(255, 255, 255, 0.3)'
                }
            },
            splitLine: {
                lineStyle: {
                    color: 'rgba(255, 255, 255, 0.1)'
                }
            },
            axisLabel: {
                color: '#4FDBFF',
                fontSize: 12,
                formatter: (value: number) => {
                    if (value >= 10000) {
                        return (value / 10000).toFixed(1) + '万'
                    }
                    return value.toString()
                }
            }
        },
        series: [
            {
                type: 'bar',
                data: chartData.data,
                barWidth: '40%',
                itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: '#00FFFF' },
                        { offset: 1, color: 'rgba(0, 255, 255, 0.3)' }
                    ])
                },
                emphasis: {
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: '#4FDBFF' },
                            { offset: 1, color: 'rgba(79, 219, 255, 0.5)' }
                        ])
                    }
                }
            }
        ]
    }

    flowChart.setOption(option, true)
}

// 初始化小时交通流量对比图
const initHourlyTrafficChart = () => {
    const chartDom = document.getElementById('hourly-traffic-chart')
    if (!chartDom) return

    // 确保图表容器有正确的尺寸
    chartDom.style.height = '100%'
    const chart = echarts.init(chartDom)

    const option = {
        backgroundColor: 'transparent',
        title: {
            text: '正在加载24小时车流量数据...',
            left: 'center',
            textStyle: {
                color: '#4fdbff',
                fontSize: 16
            }
        },
        tooltip: {
            trigger: 'axis',
            formatter: (params: any) => {
                if (params && params.length > 0) {
                    return `${params[0].axisValue}<br/>${params[0].seriesName}: ${params[0].value}辆`
                }
                return ''
            },
            backgroundColor: 'rgba(0, 20, 40, 0.8)',
            borderColor: '#4fdbff',
            textStyle: {
                color: '#fff'
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            top: '60px',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: [],
            name: '时间',
            nameTextStyle: {
                color: '#4fdbff'
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(255, 255, 255, 0.3)'
                }
            },
            axisLabel: {
                color: '#4FDBFF',
                fontSize: 12
            }
        },
        yAxis: {
            type: 'value',
            name: '车流量(辆)',
            nameTextStyle: {
                color: '#4fdbff'
            },
            axisLine: {
                show: true,
                lineStyle: {
                    color: 'rgba(255, 255, 255, 0.3)'
                }
            },
            splitLine: {
                lineStyle: {
                    color: 'rgba(255, 255, 255, 0.1)'
                }
            },
            axisLabel: {
                color: '#4FDBFF',
                fontSize: 12
            }
        },
        series: []
    }

    chart.setOption(option)

    // 窗口大小变化时重新调整图表大小
    const resizeHandler = () => {
        chart.resize()
    }
    window.addEventListener('resize', resizeHandler)

    // 返回图表实例
    return chart
}

// 初始化流量分析图
const initFlowAnalysisChart = () => {
    const chartDom = document.getElementById('flow-analysis-chart')
    if (!chartDom) return

    // 确保图表容器有正确的尺寸
    chartDom.style.height = '100%'
    const chart = echarts.init(chartDom)

    const option = {
        backgroundColor: 'transparent',
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            top: '10px',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: [],
            axisLine: {
                lineStyle: {
                    color: 'rgba(255, 255, 255, 0.3)'
                }
            },
            axisLabel: {
                color: '#4FDBFF',
                fontSize: 12
            }
        },
        yAxis: {
            type: 'value',
            axisLine: {
                show: true,
                lineStyle: {
                    color: 'rgba(255, 255, 255, 0.3)'
                }
            },
            splitLine: {
                lineStyle: {
                    color: 'rgba(255, 255, 255, 0.1)'
                }
            },
            axisLabel: {
                color: '#4FDBFF',
                fontSize: 12
            }
        },
        series: [
            {
                type: 'bar',
                data: [],
                barWidth: '40%',
                itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: '#00FFFF' },
                        { offset: 1, color: 'rgba(0, 255, 255, 0.3)' }
                    ])
                }
            }
        ]
    }

    chart.setOption(option)

    // 窗口大小变化时重新调整图表大小
    const resizeHandler = () => {
        chart.resize()
    }
    window.addEventListener('resize', resizeHandler)

    // 返回图表实例
    return chart
}

// 监听分析类型变化
watch(flowAnalysisType, async (newType) => {
    console.log('流量分析类型变化:', newType)
    // 切换类型时清空自定义日期
    if (newType !== 'custom') {
        customDateRange.value = ['', '']
    }
    await loadFlowAnalysisData()
})

// 监听自定义日期范围变化
watch(
    customDateRange,
    async (newRange) => {
        if (flowAnalysisType.value === 'custom' && newRange[0] && newRange[1]) {
            await loadFlowAnalysisData()
        }
    },
    { deep: true }
)

// 监听项目变化
watch(
    () => appStore.projectContext.selectedProjectId,
    async (newProjectId) => {
        console.log('项目ID变化:', newProjectId)
        if (newProjectId) {
            // 重新加载所有数据
            await initData()
            await loadFlowAnalysisData()
        } else {
            // 项目ID为空时设置默认数据
            setDefaultData()
        }
    }
)

// 存储图表实例，用于组件卸载时清理
let hourlyChart: any = null
let flowChart: any = null

// 页面加载时初始化
onMounted(async () => {
    console.log('页面开始初始化...')
    console.log('当前appStore状态:', appStore.projectContext)

    // 初始化数据卡片
    await initData()

    // 延迟一帧初始化图表，确保DOM已完全渲染
    setTimeout(async () => {
        console.log('开始初始化图表...')
        hourlyChart = initHourlyTrafficChart()
        flowChart = initFlowAnalysisChart()
        console.log('图表初始化完成，flowChart:', flowChart)

        // 获取24小时车流数据（在图表初始化后）
        await fetchTraffic24Hour()

        // 初始化流量分析数据
        console.log('开始加载流量分析数据...')
        console.log('当前项目ID:', appStore.projectContext.selectedProjectId)

        // 如果项目ID还没有设置，等待一段时间再重试
        if (!appStore.projectContext.selectedProjectId) {
            console.log('项目ID未设置，等待1秒后重试...')
            setTimeout(async () => {
                console.log('重试加载流量分析数据，项目ID:', appStore.projectContext.selectedProjectId)
                await loadFlowAnalysisData()
            }, 1000)
        } else {
            await loadFlowAnalysisData()
        }
    }, 100) // 增加延迟时间确保DOM完全渲染
})

// 监听车检器选择变化
watch(selectedDetector, () => {
    updateTrafficChart()
})

// 导入相关方法
const handleImport = () => {
    importDialogVisible.value = true
}
//手动更新采集最新车流量
const handleRefresh = async () => {
    await refreshTraffic24Hour()
}

const handleImportSuccess = () => {
    ElMessage.success('车流量数据导入成功！')
    // 这里可以添加刷新数据的逻辑
    // 例如：重新获取图表数据、更新统计卡片等
    refreshData()
}

// 获取24小时车流数据
const fetchTraffic24Hour = async () => {
    loading24Hour.value = true
    try {
        const response = await getTraffic24Hour()
        console.log('response=>:', response.data)
        if (response.data && Array.isArray(response.data)) {
            traffic24HourData.value = response.data

            // 提取车检器列表（去重）
            const detectors = response.data.map((tunnel: any) => tunnel.name)
            detectorList.value = detectors

            // 默认选择第一个车检器并立即更新图表
            if (detectors.length > 0) {
                selectedDetector.value = detectors[0]
                console.log('默认选择车检器:', selectedDetector.value)

                // 确保图表已初始化后再更新
                if (hourlyChart) {
                    updateTrafficChart()
                } else {
                    // 如果图表还未初始化，等待一小段时间后重试
                    setTimeout(() => {
                        if (hourlyChart) {
                            updateTrafficChart()
                        }
                    }, 200)
                }
            }
        }
    } catch (error) {
        console.error('获取24小时车流数据失败:', error)
        ElMessage.error('获取车流数据失败')
    } finally {
        loading24Hour.value = false
    }
}

// 更新交通流量图表
const updateTrafficChart = () => {
    console.log('updateTrafficChart 被调用')
    console.log('selectedDetector.value:', selectedDetector.value)
    console.log('traffic24HourData.value.length:', traffic24HourData.value.length)

    if (!selectedDetector.value || !traffic24HourData.value.length) {
        console.log('条件不满足，退出更新')
        return
    }

    // 找到选中的隧道数据
    const selectedTunnel = traffic24HourData.value.find((tunnel: any) => tunnel.name === selectedDetector.value)
    console.log('找到的隧道数据:', selectedTunnel)

    if (!selectedTunnel || !selectedTunnel.hourItems) {
        console.log('未找到隧道数据或小时数据，退出更新')
        return
    }

    // 按完整时间排序（年-月-日-小时）
    const sortedData = [...selectedTunnel.hourItems].sort((a: any, b: any) => {
        // 创建完整的时间戳进行比较
        const timeA = new Date(a.year, a.month - 1, a.day, a.hour).getTime()
        const timeB = new Date(b.year, b.month - 1, b.day, b.hour).getTime()
        return timeA - timeB
    })

    // 转换为图表数据格式
    const chartData = sortedData.map((item: any) => ({
        year: item.year,
        month: item.month,
        day: item.day,
        hour: item.hour,
        // 生成完整的时间标签
        timeLabel: formatTimeLabel(item),
        totalFlow: (item.inTruckAmount || 0) + (item.outTruckAmount || 0),
        inFlow: item.inTruckAmount || 0,
        outFlow: item.outTruckAmount || 0
    }))

    console.log('图表数据:', chartData)

    // 更新图表
    updateHourlyTrafficChart(chartData)
}

// 更新小时交通流量图表
const updateHourlyTrafficChart = (data: any[]) => {
    console.log('updateHourlyTrafficChart 被调用')
    console.log('hourlyChart 存在:', !!hourlyChart)
    console.log('数据长度:', data.length)

    if (!hourlyChart) {
        console.log('hourlyChart 不存在，退出更新')
        return
    }

    const timeLabels = data.map((item) => item.timeLabel || `${item.hour}:00`)
    const totalFlows = data.map((item) => item.totalFlow)

    console.log('时间标签:', timeLabels)
    console.log('流量数据:', totalFlows)

    const option = {
        title: {
            text: `${selectedDetector.value} - 24小时车流量`,
            left: 'center',
            textStyle: {
                color: '#4fdbff',
                fontSize: 16
            }
        },
        tooltip: {
            trigger: 'axis',
            formatter: (params: any) => {
                return `${params[0].axisValue}<br/>${params[0].seriesName}: ${params[0].value}辆`
            },
            backgroundColor: 'rgba(0, 20, 40, 0.8)',
            borderColor: '#4fdbff',
            textStyle: {
                color: '#fff'
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: timeLabels,
            name: '时间',
            nameTextStyle: {
                color: '#4fdbff'
            },
            axisLine: {
                lineStyle: {
                    color: '#4fdbff'
                }
            },
            axisLabel: {
                color: '#4fdbff'
            }
        },
        yAxis: {
            type: 'value',
            name: '车流量(辆)',
            nameTextStyle: {
                color: '#4fdbff'
            },
            axisLine: {
                lineStyle: {
                    color: '#4fdbff'
                }
            },
            axisLabel: {
                color: '#4fdbff'
            },
            splitLine: {
                lineStyle: {
                    color: 'rgba(79, 219, 255, 0.2)'
                }
            }
        },
        series: [
            {
                name: '车流量',
                type: 'line',
                data: totalFlows,
                smooth: true,
                lineStyle: {
                    color: '#4fdbff',
                    width: 3
                },
                itemStyle: {
                    color: '#4fdbff'
                },
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            {
                                offset: 0,
                                color: 'rgba(79, 219, 255, 0.3)'
                            },
                            {
                                offset: 1,
                                color: 'rgba(79, 219, 255, 0.05)'
                            }
                        ]
                    }
                }
            }
        ]
    }

    hourlyChart.setOption(option)
    console.log('图表选项已设置，标题:', option.title.text)
}

// 刷新数据方法
const refreshData = () => {
    // 重新初始化图表数据
    // 这里可以调用API获取最新数据
    console.log('刷新车流量数据...')

    // 重新获取24小时车流数据
    fetchTraffic24Hour()

    // 可以在这里添加实际的数据刷新逻辑
    // 例如：
    // - 重新获取统计卡片数据
    // - 重新获取图表数据
    // - 更新图表显示
}

// 组件卸载时清理资源
onUnmounted(() => {
    if (hourlyChart) {
        hourlyChart.dispose()
    }
    if (flowChart) {
        flowChart.dispose()
    }
})
</script>

<style lang="scss" scoped>
.traffic-flow-container {
    padding: 10px;
    height: 100%;
    background-color: #001529;
    color: #fff;
}

/* 数据卡片样式 */
.data-cards {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}

.data-card {
    flex: 1;
    background-color: rgba(0, 30, 60, 0.7);
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.data-card.empty {
    background-color: transparent;
    box-shadow: none;
}

.card-title {
    font-size: 14px;
    color: #4fdbff;
    margin-bottom: 10px;
}

.card-value {
    font-size: 28px;
    font-weight: bold;
    margin-bottom: 15px;
}

.card-value span {
    font-size: 16px;
    margin-left: 5px;
}

.card-footer {
    display: flex;
    justify-content: space-between;
}

.footer-item {
    display: flex;
    flex-direction: column;
    font-size: 12px;
}

.footer-item .up {
    color: #67c23a;
}

.footer-item .down {
    color: #f56c6c;
}

/* 图表容器样式 */
.chart-container {
    display: flex;
    gap: 10px;
    height: calc(100% - 140px);
    min-height: 400px;
}

.chart-box {
    flex: 1;
    background-color: rgba(0, 30, 60, 0.7);
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    min-height: 380px;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.chart-title {
    font-size: 16px;
    font-weight: bold;
    color: #4fdbff;
}

.chart-controls {
    display: flex;
    align-items: center;
}

.chart {
    flex: 1;
    width: 100%;
    min-height: 320px;
    position: relative;
}

.empty-data {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 300px;
}

.error-message {
    padding: 20px;
}

/* 覆盖Element Plus样式 */
:deep(.el-select .el-input__wrapper) {
    background-color: rgba(0, 20, 40, 0.7);
    box-shadow: none;
    border: 1px solid rgba(79, 219, 255, 0.3);
}

:deep(.el-select .el-input__inner) {
    color: #4fdbff;
}

:deep(.el-radio-button__inner) {
    background-color: rgba(0, 20, 40, 0.7);
    border-color: rgba(79, 219, 255, 0.3);
    color: #4fdbff;
}

:deep(.el-radio-button__original-radio:checked + .el-radio-button__inner) {
    background-color: #4fdbff;
    border-color: #4fdbff;
    color: #001529;
    box-shadow: -1px 0 0 0 #4fdbff;
}

/* 日期选择器样式 */
:deep(.el-date-editor .el-input__wrapper) {
    background-color: rgba(0, 20, 40, 0.7);
    box-shadow: none;
    border: 1px solid rgba(79, 219, 255, 0.3);
}

:deep(.el-date-editor .el-input__inner) {
    color: #4fdbff;
}

:deep(.el-date-editor .el-input__prefix) {
    color: #4fdbff;
}
</style>
