<template>
    <div class="acc-water-page">
        <div class="panel-form">
            <div class="panel-title">
                {{ isEditing ? '编辑积水点' : '新增积水点' }}
            </div>
            <el-form :model="form" :rules="rules" ref="formRef" label-width="80px" class="form-inline">
                <div class="row row-3">
                    <el-form-item label="积水点名称" prop="name">
                        <el-input v-model="form.name" placeholder="请输入积水点名称" />
                    </el-form-item>
                    <el-form-item label="经度" prop="lng">
                        <el-input-number v-model="form.lng" placeholder="请输入经度" :precision="6" style="width: 100%" />
                    </el-form-item>
                    <el-form-item label="纬度" prop="lat">
                        <el-input-number v-model="form.lat" placeholder="请输入纬度" :precision="6" style="width: 100%" />
                    </el-form-item>
                </div>
                <div class="row row-2">
                    <el-form-item label="警戒水位" prop="levLmt">
                        <el-input-number v-model="form.levLmt" placeholder="请输入警戒水位(米)" :precision="2" style="width: 100%" />
                    </el-form-item>
                    <el-form-item label="所属项目" prop="projectId">
                        <el-select v-model="form.projectId" placeholder="请选择项目" style="width: 100%">
                            <el-option v-for="project in projectList" :key="project.id" :label="project.name" :value="project.id" />
                        </el-select>
                    </el-form-item>
                </div>
                <div class="actions">
                    <el-button v-if="!isEditing" type="primary" class="confirm-btn" :loading="buttonLoading" @click="handleAdd"> 确认新增 </el-button>
                    <template v-else>
                        <el-button type="primary" class="confirm-btn" :loading="buttonLoading" @click="handleUpdate"> 确认修改 </el-button>
                        <el-button class="cancel-btn" @click="cancelEdit"> 取消编辑 </el-button>
                    </template>
                </div>
            </el-form>
        </div>

        <div class="panel panel-table">
            <!-- 搜索区域 -->
            <div class="search-form">
                <el-form :model="queryParams" :inline="true">
                    <el-form-item label="积水点名称">
                        <el-input
                            v-model="queryParams.name"
                            placeholder="请输入积水点名称"
                            clearable
                            @keyup.enter="handleQuery"
                            style="width: 200px"
                        />
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-form>
            </div>

            <!-- 表格 -->
            <el-table
                v-loading="loading"
                :data="waterDetecterList"
                style="width: 100%"
                :header-cell-style="tableHeaderStyle"
                :cell-style="tableCellStyle"
            >
                <el-table-column type="index" label="序号" width="80" :index="indexMethod" />
                <el-table-column prop="name" label="积水点名称" min-width="200" />
                <el-table-column prop="lng" label="经度" min-width="120">
                    <template #default="{ row }">
                        {{ row.lng?.toFixed(6) || '-' }}
                    </template>
                </el-table-column>
                <el-table-column prop="lat" label="纬度" min-width="120">
                    <template #default="{ row }">
                        {{ row.lat?.toFixed(6) || '-' }}
                    </template>
                </el-table-column>
                <el-table-column prop="levLmt" label="警戒水位(厘米)" min-width="120">
                    <template #default="{ row }">
                        {{ row.levLmt || '-' }}
                    </template>
                </el-table-column>
                <!-- <el-table-column prop="lastGetTime" label="最后更新时间" min-width="180" /> -->
                <el-table-column label="操作" width="160" fixed="right">
                    <template #default="{ row }">
                        <el-button type="primary" link style="color: #42f3e9" @click="handleEdit(row)">
                            <el-icon size="16"><EditPen /></el-icon>
                            修改
                        </el-button>
                        <el-button type="danger" link @click="handleDelete(row)">
                            <el-icon size="16"><Delete /></el-icon>
                            删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-wrapper">
                <pagination
                    v-show="total > 0"
                    :total="total"
                    v-model:page="queryParams.pageNum"
                    v-model:limit="queryParams.pageSize"
                    @pagination="getList"
                />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { EditPen, Delete } from '@element-plus/icons-vue'
import { useAppStore } from '@/store/modules/app'
import { listWaterDetecter, getWaterDetecter, addWaterDetecter, updateWaterDetecter, delWaterDetecter } from '@/api/subProject/health/waterDetecter'
import { WaterDetecterVO, WaterDetecterForm, WaterDetecterQuery } from '@/api/subProject/health/waterDetecter/types'
import { listProject } from '@/api/project/project'
import { ProjectVO } from '@/api/project/project/types'

const appStore = useAppStore()

// 响应式数据
const waterDetecterList = ref<WaterDetecterVO[]>([])
const projectList = ref<ProjectVO[]>([])
const loading = ref(false)
const total = ref(0)
const buttonLoading = ref(false)
const isEditing = ref(false)
const editingId = ref<string | number | undefined>()
const formRef = ref()

// 表单数据
const form = ref<WaterDetecterForm>({
    id: undefined,
    name: '',
    lng: undefined,
    lat: undefined,
    levLmt: undefined,
    projectId: undefined
})

// 查询参数
const queryParams = ref<WaterDetecterQuery>({
    pageNum: 1,
    pageSize: 9,
    name: undefined,
    projectId: undefined,
    params: {}
})

// 表单验证规则
const rules = {
    name: [{ required: true, message: '积水点名称不能为空', trigger: 'blur' }],
    lng: [{ required: true, message: '经度不能为空', trigger: 'blur' }],
    lat: [{ required: true, message: '纬度不能为空', trigger: 'blur' }],
    projectId: [{ required: true, message: '请选择所属项目', trigger: 'change' }]
}

// 获取项目列表
const getProjectList = async () => {
    try {
        const res = await listProject({
            pageNum: 1,
            pageSize: 1000,
            params: {}
        })
        projectList.value = res.rows || []
    } catch (error) {
        console.error('获取项目列表失败:', error)
        ElMessage.error('获取项目列表失败')
    }
}

// 查询列表
const getList = async () => {
    loading.value = true
    try {
        // 设置项目ID
        queryParams.value.projectId = appStore.projectContext.selectedProjectId
        const res = await listWaterDetecter(queryParams.value)
        waterDetecterList.value = res.rows || []
        total.value = res.total || 0
    } catch (error) {
        console.error('查询积水点列表失败:', error)
        ElMessage.error('查询失败')
    } finally {
        loading.value = false
    }
}

// 搜索和重置
const handleQuery = () => {
    queryParams.value.pageNum = 1
    getList()
}

const resetQuery = () => {
    queryParams.value = {
        pageNum: 1,
        pageSize: 9,
        name: undefined,
        projectId: undefined,
        params: {}
    }
    getList()
}

// 重置表单
const resetForm = () => {
    form.value = {
        id: undefined,
        name: '',
        lng: undefined,
        lat: undefined,
        levLmt: undefined,
        projectId: undefined
    }
    formRef.value?.resetFields()
}

// 新增
const handleAdd = async () => {
    if (!form.value.name || form.value.lng === undefined || form.value.lat === undefined) {
        ElMessage.warning('请完整填写积水点名称、经度和纬度')
        return
    }

    if (!form.value.projectId) {
        ElMessage.warning('请选择所属项目')
        return
    }

    buttonLoading.value = true
    try {
        // 新增时构造数据，不包含id字段
        const submitData: Omit<WaterDetecterForm, 'id'> = {
            name: form.value.name!,
            lng: Number(form.value.lng),
            lat: Number(form.value.lat),
            levLmt: form.value.levLmt ? Number(form.value.levLmt) : undefined,
            projectId: form.value.projectId
        }

        console.log('提交的数据:', submitData)

        await addWaterDetecter(submitData)
        ElMessage.success('新增成功')

        resetForm()
        await getList()
    } catch (error) {
        console.error('新增失败:', error)
        ElMessage.error('新增失败: ' + ((error as any)?.message || '未知错误'))
    } finally {
        buttonLoading.value = false
    }
}

// 编辑
const handleEdit = async (row: WaterDetecterVO) => {
    try {
        const res = await getWaterDetecter(row.id)
        const data = res.data

        form.value = {
            id: data.id,
            name: data.name,
            lng: data.lng,
            lat: data.lat,
            levLmt: data.levLmt,
            projectId: data.projectId
        }

        isEditing.value = true
        editingId.value = data.id

        // 滚动到表单区域
        document.querySelector('.panel-form')?.scrollIntoView({ behavior: 'smooth' })
    } catch (error) {
        console.error('获取积水点详情失败:', error)
        ElMessage.error('获取详情失败')
    }
}

// 修改
const handleUpdate = async () => {
    if (!form.value.name || form.value.lng === undefined || form.value.lat === undefined) {
        ElMessage.warning('请完整填写积水点名称、经度和纬度')
        return
    }

    buttonLoading.value = true
    try {
        const submitData: WaterDetecterForm = {
            id: form.value.id,
            name: form.value.name,
            lng: Number(form.value.lng),
            lat: Number(form.value.lat),
            levLmt: form.value.levLmt ? Number(form.value.levLmt) : undefined,
            projectId: form.value.projectId
        }

        await updateWaterDetecter(submitData)
        ElMessage.success('修改成功')

        cancelEdit()
        await getList()
    } catch (error) {
        console.error('修改失败:', error)
        ElMessage.error('修改失败: ' + ((error as any)?.message || '未知错误'))
    } finally {
        buttonLoading.value = false
    }
}

// 取消编辑
const cancelEdit = () => {
    isEditing.value = false
    editingId.value = undefined
    resetForm()
}

// 删除
const handleDelete = async (row: WaterDetecterVO) => {
    try {
        await ElMessageBox.confirm(`确定要删除积水点"${row.name}"吗？`, '删除确认', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        })

        await delWaterDetecter(row.id)
        ElMessage.success('删除成功')
        await getList()
    } catch (error) {
        if (error !== 'cancel') {
            console.error('删除失败:', error)
            ElMessage.error('删除失败: ' + ((error as any)?.message || '未知错误'))
        }
    }
}

// 工具方法
function indexMethod(index: number) {
    return (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1
}

function tableHeaderStyle() {
    return {
        background: 'linear-gradient(180deg, #1D2F58 0%)',
        color: '#cfe3ff',
        fontWeight: 500
    }
}

function tableCellStyle() {
    return {
        background: 'linear-gradient(0deg, rgba(8,24,61,0.8) 0%)',
        color: '#e7f1ff'
    }
}

// 生命周期
onMounted(() => {
    getProjectList()
    getList()
})
</script>

<style scoped lang="scss">
.acc-water-page {
    padding: 10px;
    color: #e7f1ff;

    min-height: calc(100vh - 120px);
    // background: red;
}

.panel-form {
    padding: 15px 30px;
    margin-bottom: 18px;
    width: 60%;
    margin-left: auto;
    margin-right: auto;
}

.panel-title {
    text-align: center;
    font-size: 22px;
    letter-spacing: 2px;
    color: #fff;
    border-bottom: none;
}

.row {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;
}

.row-3 {
    grid-template-columns: repeat(3, 1fr);
}

.row-2 {
    grid-template-columns: repeat(2, 1fr);
}

.actions {
    display: flex;
    justify-content: center;
    padding: 10px 0 6px;
}

.confirm-btn {
    padding: 5px 80px;
    background: #4286f3;
}

.cancel-btn {
    padding: 5px 40px;
    margin-left: 10px;
    background: #6c757d;
    color: #fff;
    border: none;

    &:hover {
        background: #5a6268;
    }
}

.panel {
    border: none;
    background: transparent;
    box-shadow: none;
}

.panel-table {
    padding: 10px;
}

.search-form {
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(29, 47, 88, 0.3);
    border-radius: 8px;
}

.pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    // background: red;
}

.panel-form :deep(.el-input__wrapper) {
    background-color: #2a3954;
    box-shadow: none;
    color: #e7f1ff;
    border: none;
}

.panel-form :deep(.el-textarea__inner) {
    background-color: #0e1f44;
    box-shadow: none;
    color: #e7f1ff;
    border: none;
}

/* 表格行悬浮效果 */
:deep(.el-table__row:hover) {
    background: #cfe3ff;
    cursor: pointer;
}

:deep(.el-table__row:hover td) {
    background: transparent;
}
</style>
