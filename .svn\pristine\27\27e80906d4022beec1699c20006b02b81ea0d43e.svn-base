<template>
    <div class="p-2 defect-list-page">
        <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
            <div v-show="showSearch" class="mb-[10px]">
                <el-card shadow="hover">
                    <el-form ref="queryFormRef" :model="queryParams" :inline="true">
                        <!-- <el-form-item label="项目编号" prop="projectId">
				<el-input v-model="queryParams.projectId" placeholder="请输入项目编号" clearable @keyup.enter="handleQuery" />
			  </el-form-item> -->
                        <el-form-item label="发现时间" style="width: 308px">
                            <el-date-picker
                                v-model="dateRangeDisoverTime"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                type="daterange"
                                range-separator="-"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                            />
                        </el-form-item>
                        <el-form-item label="缺陷来源" prop="source">
                            <el-select v-model="queryParams.source" placeholder="请选择缺陷来源" clearable>
                                <el-option v-for="dict in defect_source" :key="dict.value" :label="dict.label" :value="dict.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="专业类型" prop="specialty">
                            <el-select v-model="queryParams.specialty" placeholder="请选择专业类型" clearable>
                                <el-option v-for="dict in tnl_specialty" :key="dict.value" :label="dict.label" :value="dict.value" />
                            </el-select>
                        </el-form-item>
                        <!-- <el-form-item label="设备类型" prop="categoryId">
				<el-input v-model="queryParams.categoryId" placeholder="请输入设备类型" clearable @keyup.enter="handleQuery" />
			  </el-form-item> -->
                        <el-form-item label="养护策略" prop="strategy">
                            <el-select v-model="queryParams.strategy" placeholder="请选择养护策略" clearable>
                                <el-option v-for="dict in maintenance_strategy" :key="dict.value" :label="dict.label" :value="dict.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item class="filter-actions">
                            <el-button type="primary" @click="handleQuery">搜索</el-button>
                            <el-button @click="resetQuery">重置</el-button>
                        </el-form-item>
                    </el-form>
                </el-card>
            </div>
        </transition>

        <el-card shadow="never">
            <template #header>
                <div class="btn-box">
                    <div class="filter">
                        <el-radio-group v-model="filterStatus" @change="handleFilterStatus">
                            <el-radio-button value="PENDING">待处理</el-radio-button>
                            <el-radio-button value="COMPLETED">已处理</el-radio-button>
                        </el-radio-group>
                    </div>
                    <div class="export">
                        <el-row :gutter="10" class="mb8 toolbar-actions">
                            <el-col :span="1.5">
                                <el-button class="toolbar-btn btn-add" @click="handleAdd" v-hasPermi="['maintain:defect:add']">新增</el-button>
                            </el-col>
                            <el-col :span="1.5">
                                <el-button
                                    class="toolbar-btn btn-edit"
                                    :disabled="single"
                                    @click="handleUpdate()"
                                    v-hasPermi="['maintain:defect:edit']"
                                    >修改</el-button
                                >
                            </el-col>
                            <el-col :span="1.5">
                                <el-button
                                    class="toolbar-btn btn-delete"
                                    :disabled="multiple"
                                    @click="handleDelete()"
                                    v-hasPermi="['maintain:defect:delete']"
                                    >删除</el-button
                                >
                            </el-col>
                            <!-- <el-col :span="1.5" v-if="false">
                                <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['operation:discoveredDefect:export']"
                                    >导出</el-button
                                >
                            </el-col> -->
                            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
                        </el-row>
                    </div>
                </div>
            </template>

            <el-table v-loading="loading" stripe :data="discoveredDefectList" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="" align="center" prop="id" v-if="false" />
                <!-- <el-table-column label="项目编号" align="center" prop="projectId" /> -->
                <el-table-column label="发现时间" align="center" prop="disoverTime" width="180">
                    <template #default="scope">
                        <span>{{ parseTime(scope.row.disoverTime, '{y}-{m}-{d} {h}:{i}') }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="发现人" align="center" prop="disoverUserId" />
                <el-table-column label="缺陷来源" align="center" prop="source">
                    <template #default="scope">
                        <dict-tag :options="defect_source" :value="scope.row.source" />
                    </template>
                </el-table-column>
                <el-table-column label="专业类型" align="center" prop="specialty">
                    <template #default="scope">
                        <dict-tag :options="tnl_specialty" :value="scope.row.specialty" />
                    </template>
                </el-table-column>
                <el-table-column label="缺陷对象" align="center" prop="deviceName" />

                <el-table-column label="缺陷位置" align="center" prop="unitName" />
                <!-- <el-table-column label="缺陷类型" align="center" prop="defectType" >
					<template #default="scope">
                        <dict-tag :options="tnl_specialty" :value="scope.row.specialty" />
                    </template>
				</el-table-column> -->

                <el-table-column label="养护策略" align="center" prop="strategy">
                    <template #default="scope">
                        <dict-tag :options="maintenance_strategy" :value="scope.row.strategy" />
                    </template>
                </el-table-column>

                <el-table-column label="状态" align="center">
                    <template #default="scope">
                        <span v-if="scope.row.canceled === 'Y'"> 已销项 </span>
                        <span v-else-if="['observe', 'plan', 'mediumrepair', 'special'].includes(scope.row.strategy)"> 暂不处理 </span>
                        <span v-else-if="['repair', 'arrange'].includes(scope.row.strategy)">
                            <span v-if="scope.row.taskRepairStatus == 'ACTIVE'"> 处理中 </span>
                            <span v-else-if="scope.row.taskRepairStatus == 'COMPLETED'"> 已处理 </span>
                            <span v-else> 处理中 </span>
                        </span>

                        <dict-tag v-else :options="defect_status" :value="scope.row.currentStatus" />
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="300">
                    <template #default="scope">
                        <div class="op-actions">
                            <el-button link type="primary" class="op-link op-info" @click="handleView(scope.row)">
                                <img class="op-icon" src="@/assets/images/equipment-icon.png" alt="查看" />
                                查看
                            </el-button>

                            <!-- 当筛选状态为已处理时，隐藏修改、删除、处理等操作按钮 -->
                            <template v-if="filterStatus !== 'COMPLETED'">
                                <el-button
                                    link
                                    type="primary"
                                    class="op-link op-edit"
                                    @click="handleAssign(scope.row)"
                                    v-if="scope.row?.currentStatus != 'END' && isCurrentUserAssignee(scope.row.assignee)"
                                >
                                    <img class="op-icon" src="@/assets/images/edit-icon.png" alt="处理" />
                                    {{ getButtonLabel(scope.row.currentStatus) }}
                                </el-button>
                                <el-button
                                    link
                                    type="primary"
                                    class="op-link op-edit"
                                    @click="handleUpdate(scope.row)"
                                    v-hasPermi="['maintain:defect:edit']"
                                    v-if="scope.row.currentStatus == 'START'"
                                >
                                    <img class="op-icon" src="@/assets/images/edit-icon.png" alt="修改" />
                                    修改
                                </el-button>
                                <el-button
                                    link
                                    type="danger"
                                    class="op-link op-delete"
                                    @click="handleDelete(scope.row)"
                                    v-hasPermi="['maintain:defect:delete']"
                                    v-if="scope.row?.currentStatus == 'START'"
                                >
                                    <img class="op-icon" src="@/assets/images/delete-icon.png" alt="删除" />
                                    删除
                                </el-button>
                            </template>
                        </div>
                    </template>
                </el-table-column>
            </el-table>

            <pagination
                v-show="total > 0"
                :total="total"
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                @pagination="getList"
            />
        </el-card>
    </div>
</template>

<script setup name="DiscoveredDefect" lang="ts">
import { listDiscoveredDefect, getDefectData, delDiscoveredDefect } from '@/api/subProject/operation/defect'
import { DiscoveredDefectVO, DiscoveredDefectViewVO, DiscoveredDefectQuery } from '@/api/subProject/operation/defect/types'
import { useAppStore } from '@/store/modules/app'
import { useUserStore } from '@/store/modules/user'
import { ref, reactive, toRefs, onMounted, onBeforeUnmount, watch, getCurrentInstance, ComponentInternalInstance, nextTick } from 'vue'
import { useRouter } from 'vue-router'

const { proxy } = getCurrentInstance() as ComponentInternalInstance
const { tnl_specialty, defect_source, maintenance_strategy, defect_status } = toRefs<any>(
    proxy?.useDict('tnl_specialty', 'defect_source', 'maintenance_strategy', 'defect_status')
)
const router = useRouter()
const appStore = useAppStore()
const userStore = useUserStore()

const discoveredDefectList = ref<DiscoveredDefectViewVO[]>([])
const loading = ref(true)
const showSearch = ref(true)
const ids = ref<Array<string | number>>([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const dateRangeDisoverTime = ref<[DateModelType, DateModelType]>(['', ''])

const queryFormRef = ref<ElFormInstance>()

const filterStatus = ref('PENDING')

// 状态缓存相关
const CACHE_KEY = 'defect_list_search_state'

const queryParams = reactive<DiscoveredDefectQuery>({
    pageNum: 1,
    pageSize: 10,
    projectId: appStore.projectContext.selectedProjectId,
    source: undefined,
    specialty: undefined,
    categoryId: undefined,
    defectType: undefined,
    strategy: undefined,
    currentStatus: undefined,
    todoStatus: undefined,
    params: {
        disoverTime: undefined
    }
})

/** 查询缺陷管理列表 */
const getList = async () => {
    loading.value = true
    queryParams.params = {}
    queryParams.projectId = appStore.projectContext.selectedProjectId
    queryParams.todoStatus = filterStatus.value
    proxy?.addDateRange(queryParams, dateRangeDisoverTime.value, 'DisoverTime')

    const res = await listDiscoveredDefect(queryParams)
    discoveredDefectList.value = res.rows
    total.value = res.total
    loading.value = false
}

// 缓存搜索状态
const saveSearchState = () => {
    try {
        const state = {
            queryParams: {
                source: queryParams.source,
                specialty: queryParams.specialty,
                strategy: queryParams.strategy
            },
            dateRangeDisoverTime: dateRangeDisoverTime.value,
            showSearch: showSearch.value,
            filterStatus: filterStatus.value
        }
        sessionStorage.setItem(CACHE_KEY, JSON.stringify(state))
        console.log('保存缺陷管理搜索状态:', state)
    } catch (error) {
        console.error('保存缺陷管理搜索状态失败:', error)
    }
}

// 恢复搜索状态
const restoreSearchState = () => {
    try {
        const cached = sessionStorage.getItem(CACHE_KEY)
        if (cached) {
            const state = JSON.parse(cached)

            // 恢复查询参数
            if (state.queryParams) {
                queryParams.source = state.queryParams.source
                queryParams.specialty = state.queryParams.specialty
                queryParams.strategy = state.queryParams.strategy
            }

            // 恢复日期范围
            if (state.dateRangeDisoverTime) {
                dateRangeDisoverTime.value = state.dateRangeDisoverTime
            }

            // 恢复搜索框显示状态
            if (state.showSearch !== undefined) {
                showSearch.value = state.showSearch
            }

            // 恢复筛选状态
            if (state.filterStatus) {
                filterStatus.value = state.filterStatus
            }

            console.log('恢复缺陷管理搜索状态:', state)
        }
    } catch (error) {
        console.error('恢复缺陷管理搜索状态失败:', error)
    }
}

// 清除缓存状态
const clearSearchState = () => {
    sessionStorage.removeItem(CACHE_KEY)
}

/** 检查当前用户是否为指定的处理人 */
const isCurrentUserAssignee = (assignee: string | undefined | null) => {
    if (!assignee || !userStore.userId) {
        return false
    }

    // 将assignee按逗号分割，去除空格，检查是否包含当前用户ID
    const assigneeList = assignee
        .split(',')
        .map((id) => id.trim())
        .filter((id) => id)
    const currentUserId = userStore.userId.toString()

    const isAssignee = assigneeList.includes(currentUserId)

    // console.log('检查用户权限:', {
    //     currentUserId,
    //     assignee,
    //     assigneeList,
    //     isAssignee
    // })

    return isAssignee
}

const getButtonLabel = (statusValue: string) => {
    // 从defect_status数据字典中查找对应的标签
    const dictItem = defect_status.value?.find((item: any) => item.value === statusValue)

    // 如果找到字典项，返回其标签；否则返回原始值
    return dictItem?.label || statusValue
}

/** 搜索按钮操作 */
const handleQuery = () => {
    queryParams.pageNum = 1
    getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
    dateRangeDisoverTime.value = ['', '']
    queryFormRef.value?.resetFields()

    // 清除缓存状态
    clearSearchState()

    handleQuery()
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: DiscoveredDefectVO[]) => {
    ids.value = selection.map((item) => item.id)
    single.value = selection.length != 1
    multiple.value = !selection.length
}

/** 新增按钮操作 */
const handleAdd = () => {
    router.push('defectForm')
}

/** 修改按钮操作 */
const handleUpdate = async (row?: DiscoveredDefectVO) => {
    const _id = row?.id || ids.value[0]
    router.push(`defectForm?id=${_id}`)
}

/** 删除按钮操作 */
const handleDelete = async (row?: DiscoveredDefectVO) => {
    const _ids = row?.id || ids.value
    await proxy?.$modal.confirm('是否确认删除缺陷管理编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false))
    await delDiscoveredDefect(_ids)
    proxy?.$modal.msgSuccess('删除成功')
    await getList()
}

/** 导出按钮操作 */
const handleExport = () => {
    proxy?.download(
        'operation/discoveredDefect/export',
        {
            ...queryParams
        },
        `discoveredDefect_${new Date().getTime()}.xlsx`
    )
}
const handleAssign = (row: DiscoveredDefectViewVO) => {
    router.push(`defectAssign?id=${row.id}`)
}

/** 查看按钮操作 */
const handleView = (row: DiscoveredDefectViewVO) => {
    router.push(`defectDetail?id=${row.id}`)
}

/** 处理筛选状态改变 */
const handleFilterStatus = (status: string) => {
    filterStatus.value = status

    // 根据前端筛选状态设置对应的todoStatus查询条件
    queryParams.todoStatus = status

    // 重置页码并查询
    queryParams.pageNum = 1
    getList()
}

onMounted(() => {
    // 恢复搜索状态
    restoreSearchState()

    getList()
})

// 组件卸载前保存状态
onBeforeUnmount(() => {
    saveSearchState()
})

// 监听搜索条件变化，实时保存状态
watch(
    [() => queryParams.source, () => queryParams.specialty, () => queryParams.strategy, dateRangeDisoverTime, showSearch, filterStatus],
    () => {
        // 延迟保存，避免频繁操作
        nextTick(() => {
            saveSearchState()
        })
    },
    { deep: true }
)
</script>
<style lang="scss" scoped>
.defect-list-page {

    /* 头部卡片与分隔 */
    :deep(.el-card) {
        background: transparent !important;
        border: none !important;
        box-shadow: none !important;
    }

    :deep(.el-card__header) {
        border-bottom: none !important;
        padding-top: 0 !important;
    }

    :deep(.el-card__body) {
        background: transparent !important;
        padding-bottom: 0 !important;
    }

    /* 输入/下拉统一深色皮肤 */
    :deep(.el-input__wrapper),
    :deep(.el-select__wrapper),
    :deep(.el-date-editor .el-input__wrapper) {
        background: #232d45 !important;
        border-radius: 6px !important;
        box-shadow: none !important;
        min-height: 36px;
        height: 36px;
        padding: 5px 10px;
    }

    :deep(.el-input__inner::placeholder),
    :deep(.el-select__placeholder) {
        color: #8291A9 !important;
        opacity: 1;
    }

    :deep(.el-input__inner),
    :deep(.el-select .el-select__selected-item > span:not(.el-select__placeholder)) {
        color: #FFFFFF !important;
    }

    :deep(.el-select__wrapper.is-focused) {
        box-shadow: none !important;
    }

    /* 日期选择器特殊样式 */
    :deep(.el-date-editor) {
        background: #232d45 !important;
        border-radius: 6px !important;
        border: none !important;
    }

    :deep(.el-date-editor .el-input__inner) {
        color: #FFFFFF !important;
        background: transparent !important;
    }

    :deep(.el-range-separator) {
        color: #8291A9 !important;
    }

    /* 搜索/重置按钮 */
    :deep(.filter-actions .el-button:not(.is-link)) {
        border-radius: 6px !important;
        height: 40px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        font-size: 14px !important;
        margin-right: 10px;
    }

    :deep(.filter-actions .el-button.el-button--primary:not(.is-link)) {
        background-color: #4286F3 !important;
        border-color: #4286F3 !important;
        color: #FFFFFF !important;
    }

    :deep(.filter-actions .el-button.el-button--primary:not(.is-link))::before {
        content: "";
        display: inline-block;
        width: 16px;
        height: 16px;
        background: url('@/assets/images/search-icon.png') no-repeat center/contain;
        margin-right: 6px;
    }

    :deep(.filter-actions .el-button:not(.el-button--primary):not(.is-link)) {
        background-color: #808892 !important;
        border-color: #808892 !important;
        color: #FFFFFF !important;
    }

    :deep(.filter-actions .el-button:not(.el-button--primary):not(.is-link))::before {
        content: "";
        display: inline-block;
        width: 16px;
        height: 16px;
        background: url('@/assets/images/restart-icon.png') no-repeat center/contain;
        margin-right: 6px;
    }

    /* 表格透明化与去边框 */
    :deep(.el-table) {
        --el-table-bg-color: transparent;
        --el-table-header-bg-color: transparent;
        --el-table-tr-bg-color: transparent;
        --el-table-border-color: rgba(255, 255, 255, 0.08);
        background-color: transparent !important;
        color: #ffffff;
    }

    :deep(.el-table__inner-wrapper::before),
    :deep(.el-table__inner-wrapper::after),
    :deep(.el-table::before),
    :deep(.el-table--border::after),
    :deep(.el-table__border-left-patch) {
        display: none !important;
        background: transparent !important;
    }

    :deep(.el-table__header th) {
        background-color: transparent !important;
        border-bottom: none !important;
        height: 44px;
        text-align: center;
    }

    :deep(.el-table__header th .cell) {
        color: #AED7F2 !important;
        font-size: 14px !important;
        font-weight: 500 !important;
        text-align: center;
        position: relative;
        top: -3px;
    }

    :deep(.el-table td) {
        text-align: center !important;
        height: 48px;
        background-color: transparent !important;
    }

    :deep(.el-table .cell) {
        color: #FFFFFF !important;
        font-size: 13px !important;
        line-height: 1.4;
        padding: 8px 12px;
    }

    /* 整行隔行渐变 */
    :deep(.el-table__body tr) {
        background: transparent !important;
    }

    :deep(.el-table--striped .el-table__body tr.el-table__row--striped) {
        background: linear-gradient(to right, rgba(79, 158, 249, 0.02) 0%, rgba(79, 158, 249, 0.12) 60%, rgba(79, 158, 249, 0.22) 100%) !important;
    }

    :deep(.el-table__body tr:hover > td) {
        background-color: rgba(66, 134, 243, 0.08) !important;
    }

    /* 表头整行背景 */
    :deep(.el-table__header-wrapper) {
        position: relative;
    }

    :deep(.el-table__header-wrapper)::before {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        height: 44px;
        background: rgb(31, 43, 78);
        border-radius: 6px;
        pointer-events: none;
        z-index: 0;
    }

    :deep(.el-table thead),
    :deep(.el-table th.el-table__cell) {
        position: relative;
        z-index: 1;
    }

    /* 表体每行间隔线 */
    :deep(.el-table__body tr > td) {
        border-bottom: 1px solid rgba(255, 255, 255, 0.06) !important;
    }

    /* 筛选按钮组样式 */
    .btn-box {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .filter {
            flex: 1;
        }

        .export {
            margin-left: auto;
        }
    }

    :deep(.el-radio-group .el-radio-button__inner) {
        background: #232d45 !important;
        border: 1px solid #4286F3 !important;
        color: #8291A9 !important;
        border-radius: 6px !important;
        margin-right: 10px;
        height: 36px;
        line-height: 34px;
        padding: 0 16px;
    }

    :deep(.el-radio-group .el-radio-button.is-active .el-radio-button__inner) {
        background: #4286F3 !important;
        border-color: #4286F3 !important;
        color: #FFFFFF !important;
    }

    /* 顶部工具按钮 */
    .toolbar-actions {
        align-items: center;
    }

    :deep(.toolbar-btn) {
        border: none !important;
        color: #FFFFFF !important;
        font-size: 14px !important;
        height: 40px;
        padding: 0 16px 0 42px;
        border-radius: 8px;
        position: relative;
    }

    :deep(.toolbar-btn::before) {
        content: "";
        position: absolute;
        left: 14px;
        top: 50%;
        transform: translateY(-50%);
        width: 14px;
        height: 14px;
        background-repeat: no-repeat;
        background-size: contain;
    }

    :deep(.btn-add) {
        background-color: #2a59c4 !important;
    }

    :deep(.btn-add::before) {
        background-image: url('@/assets/images/add-icon.png');
    }

    :deep(.btn-edit) {
        background-color: #2ba1a0 !important;
    }

    :deep(.btn-edit::before) {
        background-image: url('@/assets/images/edit-white-icon.png');
    }

    :deep(.btn-delete) {
        background-color: #921121 !important;
    }

    :deep(.btn-delete::before) {
        background-image: url('@/assets/images/delete-white-icon.png');
    }

    /* 操作列图标按钮 */
    .op-actions {
        display: flex;
        align-items: center;
        gap: 8px;
        justify-content: center;
        padding-right: 8px;
    }

    .op-link {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        padding: 0 6px;
    }

    .op-icon {
        width: 14px;
        height: 14px;
        display: inline-block;
        margin-right: 4px;
    }

    .op-info {
        color: #4286F3 !important;
    }

    .op-edit {
        color: #42F3E9 !important;
    }

    .op-delete {
        color: #D62121 !important;
    }
}
</style>
