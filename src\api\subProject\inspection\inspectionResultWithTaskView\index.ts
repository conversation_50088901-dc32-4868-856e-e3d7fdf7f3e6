import request from '@/utils/request'
import { AxiosPromise } from 'axios'
import {
    InspectionResultWithTaskViewVO,
    InspectionResultWithTaskViewQuery,
    InspectionResultWithTaskViewForm
} from '@/api/subProject/inspection/inspectionResultWithTaskView/types'

// 定义TableDataInfo类型
interface TableDataInfo<T> {
    code: number
    msg: string
    rows: T[]
    total: number
}

/**
 * 查询巡检结果与任务视图列表
 * @param query 查询参数
 * @returns {*}
 */
export const listInspectionResultWithTaskView = (query?: InspectionResultWithTaskViewQuery): AxiosPromise<InspectionResultWithTaskViewVO[]> => {
    return request({
        url: '/inspection/device/result',
        method: 'get',
        params: query
    })
}

/**
 * 查询巡检结果与任务视图详细
 * @param id 主键ID
 * @returns {*}
 */
export const getInspectionResultWithTaskView = (id: string | number): AxiosPromise<InspectionResultWithTaskViewVO> => {
    return request({
        url: '/inspection/inspectionResultWithTaskView/' + id,
        method: 'get'
    })
}

/**
 * 导出巡检结果与任务视图
 * @param query 查询参数
 * @returns {*}
 */
export const exportInspectionResultWithTaskView = (query?: InspectionResultWithTaskViewQuery): AxiosPromise<any> => {
    return request({
        url: '/inspection/inspectionResultWithTaskView/export',
        method: 'post',
        data: query
    })
}

/**
 * 根据设备ID查询巡检结果与任务视图
 * @param deviceId 设备ID
 * @param query 其他查询参数
 * @returns {*}
 */
export const listInspectionResultByDevice = (
    deviceId: string | number,
    query?: InspectionResultWithTaskViewQuery
): AxiosPromise<InspectionResultWithTaskViewVO[]> => {
    return request({
        url: '/inspection/inspectionResultWithTaskView/device/' + deviceId,
        method: 'get',
        params: query
    })
}

/**
 * 根据线路ID查询巡检结果与任务视图
 * @param lineId 线路ID
 * @param query 其他查询参数
 * @returns {*}
 */
export const listInspectionResultByLine = (
    lineId: string | number,
    query?: InspectionResultWithTaskViewQuery
): AxiosPromise<InspectionResultWithTaskViewVO[]> => {
    return request({
        url: '/inspection/inspectionResultWithTaskView/line/' + lineId,
        method: 'get',
        params: query
    })
}

/**
 * 根据任务名称查询巡检结果与任务视图
 * @param taskName 任务名称
 * @param query 其他查询参数
 * @returns {*}
 */
export const listInspectionResultByTask = (
    taskName: string,
    query?: InspectionResultWithTaskViewQuery
): AxiosPromise<InspectionResultWithTaskViewVO[]> => {
    return request({
        url: '/inspection/inspectionResultWithTaskView/task/' + encodeURIComponent(taskName),
        method: 'get',
        params: query
    })
}

/**
 * 批量查询巡检结果与任务视图
 * @param ids 主键ID数组
 * @returns {*}
 */
export const batchGetInspectionResultWithTaskView = (ids: Array<string | number>): AxiosPromise<InspectionResultWithTaskViewVO[]> => {
    return request({
        url: '/inspection/inspectionResultWithTaskView/batch',
        method: 'post',
        data: { ids }
    })
}

/**
 * 统计巡检结果与任务视图
 * @param query 查询参数
 * @returns {*}
 */
export const statisticsInspectionResultWithTaskView = (query?: InspectionResultWithTaskViewQuery): AxiosPromise<any> => {
    return request({
        url: '/inspection/inspectionResultWithTaskView/statistics',
        method: 'get',
        params: query
    })
}
