<!-- 年度计划任务定义列表 -->
<template>
    <div class="p-2 year-define-page">
        <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
            <div v-show="showSearch" class="mb-[10px]">
                <el-card shadow="hover">
                    <el-form ref="queryFormRef" :model="queryParams" :inline="true">
                        <el-form-item>
                            <el-tree-select
                                v-model="treeSelectValue"
                                :data="planTypeList"
                                :props="{
                                    value: 'id',
                                    label: 'name',
                                    children: 'children'
                                }"
                                placeholder="请选择计划类型"
                                clearable
                                check-strictly
                                @change="handleTreeSelectChange"
                            />
                        </el-form-item>
                        <el-form-item>
                            <el-select v-model="queryParams.speciality" placeholder="请选择专业类型" clearable>
                                <el-option v-for="dict in tnl_specialty" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-select v-model="queryParams.frequencyType" placeholder="请选择养护频次单位" clearable>
                                <el-option v-for="dict in cur_frequency_unit" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item class="freq-item">
                            <el-input placeholder="选择频次" type="number" v-model="queryParams.frequency" />
                        </el-form-item>
                        <el-form-item>
                            <el-select v-model="queryParams.publishState" placeholder="请选择计划状态" clearable>
                                <el-option v-for="dict in plan_publish_state" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-input placeholder="年度计划名称" v-model="queryParams.name" clearable />
                        </el-form-item>
                        <el-form-item class="filter-actions">
                            <el-button type="primary" @click="handleQuery">搜索</el-button>
                        </el-form-item>
                    </el-form>
                </el-card>
            </div>
        </transition>
        <el-card shadow="never">
            <template #header>
                <el-row :gutter="10" class="mb8 toolbar-actions">
                    <!--<el-col :span="1.5">
                        <el-button class="toolbar-btn btn-add" @click="handleAdd">新增</el-button>
                    </el-col>
                     <el-col :span="1.5">
                        <el-button class="toolbar-btn btn-edit" :disabled="single" @click="handleUpdate()">修改</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button class="toolbar-btn btn-delete" :disabled="multiple" @click="handleDelete()">删除</el-button>
                    </el-col> -->
                    <el-col :span="1.5">
                        <el-button class="toolbar-btn btn-export" @click="handleExport" v-hasPermi="['plan:yearPlanCatalog:export']">导出</el-button>
                    </el-col>
                    <el-col :span="1.5" v-if="buttonsEnabled.audit">
                        <el-button class="toolbar-btn btn-batch-audit" @click="handleDialog('批量审核')">批量审核</el-button>
                    </el-col>
                    <el-col :span="1.5" v-if="buttonsEnabled.confirm">
                        <el-button class="toolbar-btn btn-batch-confirm" @click="handleDialog('批量确认')">批量确认</el-button>
                    </el-col>
                    <el-col :span="1.5" v-if="buttonsEnabled.publish">
                        <el-button class="toolbar-btn btn-batch-publish" @click="handleDialog('批量发布')">批量发布</el-button>
                    </el-col>
                    <el-col :span="1.5" v-if="buttonsEnabled.addInspection">
                        <el-button class="toolbar-btn btn-add-inspect" @click="router.push(`addInspection?yearTaskId=${yearTaskId}`)"
                            >新增巡检计划</el-button
                        >
                    </el-col>
                    <el-col :span="1.5" v-if="buttonsEnabled.addMaintian">
                        <el-button class="toolbar-btn btn-add-curing" @click="router.push(`addMaintian?yearTaskId=${yearTaskId}`)"
                            >新增养护计划</el-button
                        >
                    </el-col>
                    <el-col :span="1.5" v-if="buttonsEnabled.addSealing">
                        <el-button class="toolbar-btn btn-add-sealing" @click="router.push(`addSealing?yearTaskId=${yearTaskId}`)"
                            >新增封道计划</el-button
                        >
                    </el-col>
                </el-row>
            </template>
            <el-table v-loading="loading" :data="taskDefineList" @selection-change="handleSelectionChange" stripe>
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="序号" type="index" width="55" align="center" :index="1" />
                <el-table-column label="" align="center" prop="id" v-if="false" />
                <el-table-column label="时间" align="center">
                    <template #default="scope"> {{ scope.row.year }}年 </template>
                </el-table-column>
                <el-table-column label="计划类型" align="center">
                    <template #default="scope">
                        {{ plan_type?.find((dict) => dict.value === scope.row.taskType)?.label || scope.row.taskType }}
                    </template>
                </el-table-column>
                <el-table-column label="专业类型" align="center" prop="speciality">
                    <template #default="scope">
                        {{ tnl_specialty?.find((dict) => dict.value === scope.row.speciality)?.label || scope.row.speciality }}
                    </template>
                </el-table-column>
                <el-table-column label="年度计划名称" align="center" prop="name" />
                <el-table-column label="维修养护内容" align="center" prop="maintenanceContent">
                    <template #default="scope">
                        {{ maintenance_content?.find((dict) => dict.value === scope.row.maintenanceContent)?.label || scope.row.maintenanceContent }}
                    </template>
                </el-table-column>
                <el-table-column label="频次" align="center" prop="frequency" />
                <el-table-column label="频次单位" align="center" prop="frequencyType">
                    <template #default="scope">
                        {{ cur_frequency_unit?.find((dict) => dict.value === scope.row.frequencyType)?.label || scope.row.frequencyType }}
                    </template>
                </el-table-column>

                <el-table-column label="发布状态" align="center">
                    <template #default="scope">
                        <dict-tag :options="plan_publish_state" :value="scope.row.publishState" />
                    </template>
                </el-table-column>
                <el-table-column label="任务状态" align="center">
                    <template #default="scope">
                        <el-tag v-if="scope.row.status === 'TERMINATED'" type="danger" size="small">已停用</el-tag>
                        <el-tag v-else type="success" size="small">正常</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="300" class-name="small-padding">
                    <template #default="scope">
                        <div class="op-actions">
                            <el-button link type="primary" class="op-link op-info" @click="handleView(scope.row)">
                                <img class="op-icon" src="@/assets/images/equipment-icon.png" alt="查看" />查看
                            </el-button>
                            <el-button
                                link
                                type="primary"
                                class="op-link op-edit"
                                v-if="buttonsEnabled.copy"
                                @click="handleCopy(scope.row)"
                                :title="'复制到其他年度（将创建临时计划）'"
                            >
                                <img class="op-icon" src="@/assets/images/edit-icon.png" alt="复制" />复制
                            </el-button>
                            <el-button
                                link
                                type="primary"
                                class="op-link op-edit"
                                v-if="buttonsEnabled.edit && scope.row.publishState != 'published'"
                                @click="handleUpdate(scope.row)"
                            >
                                <img class="op-icon" src="@/assets/images/edit-icon.png" alt="编辑" />编辑
                            </el-button>
                            <el-button
                                link
                                type="danger"
                                class="op-link op-delete"
                                v-if="buttonsEnabled.delete && scope.row.publishState != 'published'"
                                @click="handleDelete(scope.row)"
                            >
                                <img class="op-icon" src="@/assets/images/delete-icon.png" alt="删除" />删除
                            </el-button>
                            <el-button
                                link
                                type="danger"
                                class="op-link op-delete"
                                v-if="buttonsEnabled.terminate && scope.row.status !== 'TERMINATED' && scope.row.publishState == 'published'"
                                @click="handleTerminate(scope.row)"
                                :title="'停用年度计划任务'"
                            >
                                <img class="op-icon" src="@/assets/images/delete-icon.png" alt="停用" />停用
                            </el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>

            <pagination
                v-show="total > 0"
                :total="total"
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                @pagination="getList"
            />
        </el-card>
        <el-dialog :title="dialog.title" v-model="dialog.visible" width="1000px" append-to-body>
            <TaskDefineSel
                ref="taskDefineSelRef"
                :statusConfig="statusConfig"
                :projectId="queryParams.projectId"
                @confirm="handleTaskDefineConfirm"
                @cancel="handleTaskDefineCancel"
            />
        </el-dialog>

        <!-- 复制对话框 -->
        <CopyPlanDialog v-model="copyDialogVisible" :source-define="selectedDefine" @success="handleCopySuccess" />
    </div>
</template>

<script setup lang="ts">
import { listTaskDefine, getTaskDefine, delTaskDefine, addTaskDefine, updateTaskDefine, terminateTask } from '@/api/plan/taskDefine'
import { TaskDefineVO, TaskDefineQuery, TaskDefineForm } from '@/api/plan/taskDefine/types'
import TaskDefineSel from '../../components/TaskDefineSel.vue'
import CopyPlanDialog from '../../components/CopyPlanDialog.vue'
import { useRouter, useRoute } from 'vue-router'
import { nextTick } from 'vue'
import { TunnelTreeNode } from '@/api/types'
import { useAppStore } from '@/store/modules/app'

const { proxy } = getCurrentInstance() as ComponentInternalInstance
const { tnl_specialty, cur_frequency_unit, plan_type, maintenance_content, inspection_work_type, plan_publish_state, block_road_content } =
    toRefs<any>(
        proxy?.useDict(
            'tnl_specialty',
            'cur_frequency_unit',
            'plan_type',
            'maintenance_content',
            'plan_publish_state',
            'inspection_work_type',
            'block_road_content'
        )
    )

const router = useRouter()
const route = useRoute()
const appStore = useAppStore()
const planTypeList = ref<TunnelTreeNode[]>()
const selectPlanType = ref<string>()
const taskDefineList = ref<TaskDefineVO[]>([])
const buttonLoading = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref<Array<string | number>>([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const queryFormRef = ref<ElFormInstance>()
const yearPlanCatalogFormRef = ref<ElFormInstance>()
const taskDefineSelRef = ref()
const buttonsEnabled = ref({
    addInspection: appStore.judgePermissionEnabled('yearplan:inspect:add'),
    addMaintian: appStore.judgePermissionEnabled('yearplan:curing:add'),
    addSealing: appStore.judgePermissionEnabled('yearplan:sealing:add'),
    copy: appStore.judgePermissionEnabled('yearplan:copy'),
    audit: appStore.judgePermissionEnabled('yearplan:audit'),
    confirm: appStore.judgePermissionEnabled('yearplan:confirm'),
    publish: appStore.judgePermissionEnabled('yearplan:publish'),
    edit: appStore.judgePermissionEnabled('yearplan:edit'),
    delete: appStore.judgePermissionEnabled('yearplan:delete'),
    terminate: appStore.judgePermissionEnabled('yearplan:stop') // 停用权限 appStore.judgePermissionEnabled('yearplan:stop')
})

const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
})

// 状态配置，用于传递给TaskDefineSel组件
const statusConfig = ref({
    filterStatus: '', // 用于筛选的状态
    updateStatus: '' // 确认后要更新的状态
})

// 复制相关状态
const copyDialogVisible = ref(false)
const selectedDefine = ref<TaskDefineVO>()

const initFormData: TaskDefineForm = {
    id: undefined,
    projectId: undefined,
    yearTaskId: undefined,
    name: undefined,
    taskType: undefined,
    speciality: undefined,
    year: undefined,
    frequencyType: undefined,
    frequency: undefined,
    frequencyData: undefined,
    bgnDate: undefined,
    endDate: undefined,
    tempTask: undefined,
    inspectionIds: undefined,
    teamIds: undefined,
    taskResourceBo: undefined,
    maintenanceContent: undefined,
    status: undefined,
    publishState: undefined
}

const data = reactive<PageData<TaskDefineForm, TaskDefineQuery>>({
    form: { ...initFormData },
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectId: undefined,
        yearTaskId: undefined,
        name: undefined,
        taskType: undefined,
        speciality: undefined,
        year: undefined,
        frequencyType: undefined,
        frequency: undefined,
        frequencyData: undefined,
        bgnDate: undefined,
        endDate: undefined,
        tempTask: undefined,
        maintenanceContent: undefined,
        status: undefined,
        publishState: undefined,
        params: {},
        orderByColumn: 'id',
        isAsc: 'desc'
    },
    rules: {
        id: [{ required: true, message: '不能为空', trigger: 'blur' }]
    }
})

const { queryParams, form, rules } = toRefs(data)

const treeSelectValue = ref()

const handleTreeSelectChange = (value: string, node: any) => {
    // 清空之前的值
    queryParams.value.taskType = undefined
    queryParams.value.maintenanceContent = undefined

    if (!value) return

    // 判断是否为第一层级
    const isFirstLevel = planTypeList.value?.some((item) => item.id === value)
    if (isFirstLevel) {
        queryParams.value.taskType = value
    } else {
        // 找到父节点的id作为taskType
        const parentNode = planTypeList.value?.find((item) => item.children?.some((child) => child.id === value))
        if (parentNode) {
            queryParams.value.taskType = parentNode.id
            queryParams.value.maintenanceContent = value
        }
    }
    handleQuery()
}

/** 查询任务定义列表 */
const getList = async () => {
    loading.value = true
    try {
        const res = await listTaskDefine(queryParams.value)
        console.log(res.data)
        taskDefineList.value = res.rows
        console.log(taskDefineList.value)
        total.value = res.total
    } catch (error) {
        console.error('获取任务定义列表失败:', error)
    } finally {
        loading.value = false
    }
}

/** 取消按钮 */
const cancel = () => {
    reset()
    dialog.visible = false
}

/** 表单重置 */
const reset = () => {
    form.value = { ...initFormData }
    yearPlanCatalogFormRef.value?.resetFields()
}

/** 搜索按钮操作 */
const handleQuery = () => {
    queryParams.value.pageNum = 1
    queryParams.value.projectId = appStore.projectContext.selectedProjectId
    queryParams.value.yearTaskId = route.query.yearTaskId as string
    queryParams.value.tempTask = 'NO'
    getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
    queryFormRef.value?.resetFields()
    queryParams.value.projectId = appStore.projectContext.selectedProjectId
    queryParams.value.yearTaskId = route.query.yearTaskId as string
    handleQuery()
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: TaskDefineVO[]) => {
    ids.value = selection.map((item) => item.id)
    single.value = selection.length != 1
    multiple.value = !selection.length
}

/** 新增对话框显示操作 */
const handleDialog = (type: string) => {
    console.log('queryParams.projectId', appStore.projectContext.selectedProjectId)
    // 确保projectId已经设置
    if (!queryParams.value.projectId) {
        queryParams.value.projectId = appStore.projectContext.selectedProjectId
    }

    // 检查projectId是否有效
    if (!queryParams.value.projectId) {
        proxy?.$modal.msgError('项目信息未加载完成，请稍后再试')
        return
    }

    // 根据操作类型设置状态配置
    switch (type) {
        //设置成正确的状态
        case '批量审核':
            statusConfig.value = {
                filterStatus: 'temp', // 筛选待审批的任务
                updateStatus: 'confirm' // 更新为审核中状态
            }
            break
        case '批量确认':
            statusConfig.value = {
                filterStatus: 'confirm', // 筛选待确认的任务
                updateStatus: 'publish' // 更新为已确认状态
            }
            break
        case '批量发布':
            statusConfig.value = {
                filterStatus: 'publish', // 筛选待发布的任务
                updateStatus: 'published' // 更新为已发布状态
            }
            break
        default:
            statusConfig.value = {
                filterStatus: '',
                updateStatus: ''
            }
    }

    // 打开对话框
    dialog.visible = true
    dialog.title = type

    // 等待对话框渲染完成后加载数据
    nextTick(() => {
        if (taskDefineSelRef.value) {
            taskDefineSelRef.value.getList()
        }
    })
}

/** TaskDefineSel组件确认事件处理 */
const handleTaskDefineConfirm = async (data: any) => {
    try {
        proxy?.$modal.msgSuccess(`${dialog.title}操作成功，共处理 ${data.ids.length} 条记录`)
        dialog.visible = false
        // 刷新列表数据
        await getList()
    } catch (error) {
        console.error(`${dialog.title}操作失败:`, error)
        proxy?.$modal.msgError(`${dialog.title}操作失败`)
    }
}

/** TaskDefineSel组件取消事件处理 */
const handleTaskDefineCancel = () => {
    dialog.visible = false
}

/** 新增按钮操作 */
const handleAdd = () => {
    reset()
    dialog.visible = true
    dialog.title = '添加年度计划目录'
}

/** 修改按钮操作 */
const handleUpdate = async (row?: TaskDefineVO) => {
    reset()
    const _id = row?.id || ids.value[0]

    // 根据taskType跳转到不同的编辑页面
    const taskType = row?.taskType
    let editPath = ''
    switch (taskType) {
        case 'inspect':
            editPath = 'addInspection'
            break
        case 'curing':
            editPath = 'addMaintian'
            break
        case 'sealing':
            editPath = 'addSealing'
            break
        default:
            editPath = 'addInspection'
    }
    router.push(`${editPath}?yearTaskId=${yearTaskId.value}&id=${_id}`)
}
const handleView = (row: TaskDefineVO) => {
    const _id = row?.id
    // router.push(`../year/planBaseInfo?id=${_id}&taskType=${row.taskType}&from=define`);
    router.push(`../plan/baseInfo?id=${_id}&taskType=${row.taskType}&from=define`)
}

/** 提交按钮 */
const submitForm = () => {
    yearPlanCatalogFormRef.value?.validate(async (valid: boolean) => {
        if (valid) {
            buttonLoading.value = true
            if (form.value.id) {
                await updateTaskDefine(form.value).finally(() => (buttonLoading.value = false))
            } else {
                await addTaskDefine(form.value).finally(() => (buttonLoading.value = false))
            }
            proxy?.$modal.msgSuccess('操作成功')
            dialog.visible = false
            await getList()
        }
    })
}

/** 删除按钮操作 */
const handleDelete = async (row?: TaskDefineVO) => {
    const _ids = row?.id || ids.value
    await proxy?.$modal.confirm('是否确认删除年度计划目录编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false))
    await delTaskDefine(_ids)
    proxy?.$modal.msgSuccess('删除成功')
    await getList()
}

/** 导出按钮操作 */
const handleExport = () => {
    proxy?.download(
        'plan/yearPlanCatalog/export',
        {
            ...queryParams.value
        },
        `yearPlanCatalog_${new Date().getTime()}.xlsx`
    )
}

/** 处理复制按钮点击 */
const handleCopy = (row: TaskDefineVO) => {
    selectedDefine.value = row
    copyDialogVisible.value = true
}

/** 处理复制成功 */
const handleCopySuccess = () => {
    // 刷新列表
    getList()
    proxy?.$modal.msgSuccess('年度计划复制成功，已创建为待发布计划，您可以在列表中查看和编辑')
}

/** 停用年度计划任务 */
const handleTerminate = async (row: TaskDefineVO) => {
    try {
        // 显示确认对话框
        await proxy?.$modal.confirm(
            `确定要停用年度计划任务"${row.name}"吗？\n\n停用后将会：\n• 将任务定义状态设为停用\n• 停用所有相关的月度任务和任务\n• 停用相关的工作流实例和待办\n\n此操作不可逆，请谨慎操作！`
        )

        // 执行停用操作
        loading.value = true
        const response = await terminateTask(row.id!)

        // 获取返回的统计数据
        const result = response.data

        // 显示详细的成功消息
        const message = `停用成功！影响记录数：\n• 任务定义：${result.taskDefineCount}条\n• 月度任务：${result.taskMonthCount}条\n• 任务：${result.taskCount}条\n• 工作流实例：${result.workflowInstanceCount}条\n• 待办：${result.workflowTodoCount}条`

        proxy?.$modal.msgSuccess(message)

        // 刷新列表
        await getList()
    } catch (error: any) {
        console.error('停用年度计划任务失败:', error)
        if (error !== 'cancel') {
            proxy?.$modal.msgError('停用失败：' + (error.message || '未知错误'))
        }
    } finally {
        loading.value = false
    }
}
const yearTaskId = ref<string>()

/** 根据ID获取数据 */
const getInfo = async (id: string) => {
    try {
        const res = await getTaskDefine(id)
        Object.assign(form.value, res.data)
    } catch (error) {
        console.error('获取任务定义详情失败:', error)
        proxy?.$modal.msgError('获取详情失败')
    }
}

onMounted(async () => {
    yearTaskId.value = route.query.yearTaskId as string
    queryParams.value.projectId = appStore.projectContext.selectedProjectId
    queryParams.value.yearTaskId = route.query.yearTaskId as string
    console.log('queryParams.projectId', queryParams.value.projectId)
    // 如果有queryId，则获取详情数据
    const queryId = route.query.id
    if (queryId) {
        await getInfo(queryId as string)
    }

    getList()
    planTypeList.value = []
})

watch(
    [() => plan_type.value, () => maintenance_content.value, () => inspection_work_type.value, () => block_road_content.value],
    ([planTypes, maintenanceTypes, inspectionTypes, blockRoadTypes]) => {
        // 检查所有数据源是否都已加载
        if (!planTypes || !maintenanceTypes || !inspectionTypes || !blockRoadTypes) {
            return
        }

        planTypeList.value = []

        planTypes.forEach((item) => {
            if (item.value == 'curing') {
                const curingNode = {
                    name: item.label,
                    id: item.value,
                    children: maintenanceTypes.map((content: any) => ({
                        name: content.label,
                        id: content.value,
                        children: []
                    }))
                }
                planTypeList.value.push(curingNode)
            } else if (item.value == 'inspect') {
                const curingNode = {
                    name: item.label,
                    id: item.value,
                    children: inspectionTypes.map((content: any) => ({
                        name: content.label,
                        id: content.value,
                        children: []
                    }))
                }
                planTypeList.value.push(curingNode)
            } else if (item.value == 'block_road') {
                const curingNode = {
                    name: item.label,
                    id: item.value,
                    children: blockRoadTypes.map((content: any) => ({
                        name: content.label,
                        id: content.value,
                        children: []
                    }))
                }
                planTypeList.value.push(curingNode)
            }
        })
    },
    {
        deep: true,
        immediate: true
    }
)
</script>

<style lang="scss" scoped>
.year-define-page {
    /* 让查询表单不换行 */
    :deep(.el-form--inline) {
        flex-wrap: nowrap !important;
        align-items: center;
    }

    :deep(.el-card) {
        background: transparent !important;
        border: none !important;
        box-shadow: none !important;
    }

    :deep(.el-card__header) {
        border-bottom: none !important;
        padding-top: 0 !important;
    }

    :deep(.el-card__body) {
        background: transparent !important;
        padding-bottom: 0 !important;
    }

    :deep(.el-input__wrapper),
    :deep(.el-select__wrapper) {
        background: #232d45 !important;
        border-radius: 6px !important;
        box-shadow: none !important;
        min-height: 36px;
        height: 36px;
        padding: 5px 10px;
    }

    :deep(.el-input__inner::placeholder),
    :deep(.el-select__placeholder) {
        color: #8291a9 !important;
        opacity: 1;
    }

    :deep(.el-input__inner),
    :deep(.el-select .el-select__selected-item > span:not(.el-select__placeholder)) {
        color: #ffffff !important;
    }

    :deep(.el-select__wrapper.is-focused) {
        box-shadow: none !important;
    }

    :deep(.filter-actions .el-button:not(.is-link)) {
        border-radius: 6px !important;
        height: 40px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        font-size: 14px !important;
    }

    :deep(.filter-actions .el-button.el-button--primary:not(.is-link)) {
        background-color: #4286f3 !important;
        border-color: #4286f3 !important;
        color: #ffffff !important;
    }

    :deep(.filter-actions .el-button.el-button--primary:not(.is-link))::before {
        content: '';
        display: inline-block;
        width: 16px;
        height: 16px;
        background: url('@/assets/images/search-icon.png') no-repeat center/contain;
        margin-right: 6px;
    }

    :deep(.el-table) {
        --el-table-bg-color: transparent;
        --el-table-header-bg-color: transparent;
        --el-table-tr-bg-color: transparent;
        --el-table-border-color: rgba(255, 255, 255, 0.08);
        background-color: transparent !important;
        color: #ffffff;
    }

    :deep(.el-table__inner-wrapper::before),
    :deep(.el-table__inner-wrapper::after),
    :deep(.el-table::before),
    :deep(.el-table--border::after),
    :deep(.el-table__border-left-patch) {
        display: none !important;
        background: transparent !important;
    }

    :deep(.el-table__header th) {
        background-color: transparent !important;
        border-bottom: none !important;
        height: 44px;
        text-align: center;
    }

    :deep(.el-table__header th .cell) {
        color: #aed7f2 !important;
        font-size: 14px !important;
        font-weight: 500 !important;
        text-align: center;
        position: relative;
        top: -2px;
    }

    :deep(.el-table td) {
        text-align: center !important;
        height: 48px;
        background-color: transparent !important;
    }

    :deep(.el-table .cell) {
        color: #ffffff !important;
        font-size: 13px !important;
        line-height: 1.4;
        padding: 8px 12px;
    }

    :deep(.el-table__body tr) {
        background: transparent !important;
    }

    :deep(.el-table--striped .el-table__body tr.el-table__row--striped) {
        background: linear-gradient(to right, rgba(79, 158, 249, 0.02) 0%, rgba(79, 158, 249, 0.12) 60%, rgba(79, 158, 249, 0.22) 100%) !important;
    }

    :deep(.el-table__body tr:hover > td) {
        background-color: rgba(66, 134, 243, 0.08) !important;
    }

    :deep(.el-table__header-wrapper) {
        position: relative;
    }

    :deep(.el-table__header-wrapper)::before {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        height: 44px;
        background: rgb(31, 43, 78);
        border-radius: 6px;
        pointer-events: none;
        z-index: 0;
    }

    :deep(.el-table thead),
    :deep(.el-table th.el-table__cell) {
        position: relative;
        z-index: 1;
    }

    :deep(.el-table__body tr > td) {
        border-bottom: 1px solid rgba(255, 255, 255, 0.06) !important;
    }

    /* 顶部工具按钮样式 */
    .toolbar-actions {
        align-items: center;
        justify-content: flex-start;
        flex-wrap: wrap;
        row-gap: 8px;
    }

    :deep(.toolbar-btn) {
        border: none !important;
        color: #ffffff !important;
        font-size: 14px !important;
        height: 40px;
        padding: 0 16px;
        border-radius: 8px;
    }

    :deep(.btn-add) {
        background-color: #2a59c4 !important;
    }
    :deep(.btn-edit) {
        background-color: #2ba1a0 !important;
    }
    :deep(.btn-delete) {
        background-color: #921121 !important;
    }
    :deep(.btn-export) {
        background-color: #19ab5a !important;
    }
    :deep(.btn-batch-audit) {
        background-color: #6b5cf6 !important;
    }
    :deep(.btn-batch-confirm) {
        background-color: #1e9dd8 !important;
    }
    :deep(.btn-batch-publish) {
        background-color: #e0861a !important;
    }
    :deep(.btn-add-inspect) {
        background-color: #2a59c4 !important;
    }
    :deep(.btn-add-curing) {
        background-color: #19ab5a !important;
    }
    :deep(.btn-add-sealing) {
        background-color: #e25b2a !important;
    }

    /* 仅为新增/修改/删除/导出添加图标，不影响其它批量按钮 */
    :deep(.btn-add),
    :deep(.btn-edit),
    :deep(.btn-delete),
    :deep(.btn-export) {
        position: relative;
        padding-left: 42px;
    }

    :deep(.btn-add::before),
    :deep(.btn-edit::before),
    :deep(.btn-delete::before),
    :deep(.btn-export::before) {
        content: '';
        position: absolute;
        left: 14px;
        top: 50%;
        transform: translateY(-50%);
        width: 14px;
        height: 14px;
        background-repeat: no-repeat;
        background-size: contain;
    }

    :deep(.btn-add::before) {
        background-image: url('@/assets/images/add-icon.png');
    }
    :deep(.btn-edit::before) {
        background-image: url('@/assets/images/edit-white-icon.png');
    }
    :deep(.btn-delete::before) {
        background-image: url('@/assets/images/delete-white-icon.png');
    }
    :deep(.btn-export::before) {
        background-image: url('@/assets/images/export-icon.png');
    }

    :deep(.right-toolbar) {
        display: none !important;
    }

    .op-actions {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .op-link {
        display: inline-flex;
        align-items: center;
        // gap: 6px;
        margin: 0;
        padding: 10px;
// background: red;
    }

    .op-icon {
        width: 14px;
        height: 14px;
        display: inline-block;
        margin-right: 4px;
    }

    .op-info {
        color: #4286f3 !important;
    }

    .op-edit {
        color: #42f3e9 !important;
    }

    .op-delete {
        color: #d62121 !important;
    }

    /* 使“选择频次”更窄：仅影响该项 */
    :deep(.freq-item) {
        width: 140px !important;
    }

    :deep(.freq-item .el-form-item__content) {
        width: 140px !important;
    }

    :deep(.freq-item .el-input),
    :deep(.freq-item .el-input__wrapper) {
        width: 140px !important;
    }
}
</style>
