<template>
    <div class="p-2 h-full flex flex-col">
        <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
            <div v-show="showSearch" class="mb-[10px]">
                <el-card shadow="hover">
                    <el-form ref="queryFormRef" :model="queryParams" :inline="true">
                        <el-form-item label="入库单号" prop="orderSn">
                            <el-input v-model="queryParams.orderSn" placeholder="请输入入库单号" clearable @keyup.enter="handleQuery" />
                        </el-form-item>
                        <el-form-item label="出库时间" style="width: 308px">
                            <el-date-picker
                                v-model="dateRangeOutDate"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                type="daterange"
                                range-separator="-"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                            />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                        </el-form-item>
                    </el-form>
                </el-card>
            </div>
        </transition>

        <!-- 操作按钮区域 -->
        <el-card shadow="never" class="mb-4">
            <template #header>
                <el-row :gutter="10" class="mb8">
                    <el-col :span="1.5">
                        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['common:resourceOutOrder:add']">新增</el-button>
                    </el-col>
                    <!-- v-hasPermi="['common:resourceOutOrder:edit']" -->
                    <el-col :span="1.5">
                        <el-button
                            type="success"
                            plain
                            icon="Edit"
                            v-hasPermi="['common:resourceOutOrder:edit']"
                            :disabled="single"
                            @click="handleUpdate()"
                            >修改</el-button
                        >
                    </el-col>
                    <!-- v-hasPermi="['common:resourceOutOrder:remove']" -->
                    <el-col :span="1.5">
                        <el-button
                            type="danger"
                            v-hasPermi="['common:resourceOutOrder:remove']"
                            plain
                            icon="Delete"
                            :disabled="multiple"
                            @click="handleDelete()"
                            >删除</el-button
                        >
                    </el-col>
                    <!-- <el-col :span="1.5">
                        <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['common:resourceOutOrder:export']"
                            >导出</el-button
                        >
                    </el-col> -->
                    <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
                </el-row>
            </template>
        </el-card>

        <!-- 主表区域 (上半部分) -->
        <el-card shadow="never" class="flex-shrink-0" style="height: 45vh; min-height: 400px">
            <!-- <template #header>
                <div class="flex justify-between items-center">
                    <span>出库单列表</span>
                    <el-tag v-if="selectedOutOrder" type="warning"> 已选择: {{ selectedOutOrder.orderSn }} </el-tag>
                </div>
            </template> -->

            <div class="h-full overflow-hidden flex flex-col">
                <el-table
                    v-loading="loading"
                    :data="resourceOutOrderList"
                    @selection-change="handleSelectionChange"
                    @row-click="handleRowClick"
                    :row-class-name="getRowClassName"
                    highlight-current-row
                    height="100%"
                    class="flex-1"
                >
                    <el-table-column type="selection" width="55" align="center" />
                    <el-table-column label="出库单号" align="center" prop="orderSn" />
                    <el-table-column label="出库类型" align="center" prop="stockOutType">
                        <template #default="scope">
                            <dict-tag :options="resource_outstock_way" :value="scope.row.stockOutType" />
                        </template>
                    </el-table-column>
                    <el-table-column label="出库金额" align="center" prop="amount" />
                    <el-table-column label="备注" align="center" prop="remark" />
                    <el-table-column label="状态" align="center" prop="status">
                        <template #default="scope">
                            <dict-tag :options="resource_out_order_status" :value="scope.row.status" />
                        </template>
                    </el-table-column>
                    <el-table-column label="出库时间" align="center" prop="outDate" width="180">
                        <template #default="scope">
                            <span>{{ parseTime(scope.row.outDate, '{y}-{m}-{d}') }}</span>
                        </template>
                    </el-table-column>

                    <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                        <template #default="scope">
                            <!-- <el-button v-if="scope.row.taskId" link type="primary" @click="handleViewTask(scope.row.id)">查看关联作业</el-button> -->

                            <!-- v-hasPermi="['common:resourceOutOrder:confirm']" -->
                            <el-button
                                v-hasPermi="['common:resourceOutOrder:confirm']"
                                :disabled="scope.row.status != 'toconfirm'"
                                link
                                type="primary"
                                @click="handleConfirmOutbound(scope.row)"
                                >确认出库</el-button
                            >
                            <!-- v-hasPermi="['common:resourceOutOrder:edit']" -->
                            <el-button
                                v-hasPermi="['common:resourceOutOrder:edit']"
                                link
                                type="primary"
                                :disabled="scope.row.status == 'confirmed'"
                                @click="handleUpdate(scope.row)"
                                >修改</el-button
                            >
                            <!-- v-hasPermi="['common:resourceOutOrder:remove']" -->
                            <el-button
                                v-hasPermi="['common:resourceOutOrder:remove']"
                                link
                                :disabled="scope.row.status == 'confirmed'"
                                type="primary"
                                @click="handleDelete(scope.row)"
                                >删除</el-button
                            >
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 主表分页 -->
                <div class="mt-4">
                    <pagination
                        v-show="total > 0"
                        :total="total"
                        v-model:page="queryParams.pageNum"
                        v-model:limit="queryParams.pageSize"
                        @pagination="getList"
                    />
                </div>
            </div>
        </el-card>

        <!-- 明细表区域 (下半部分) -->
        <el-card shadow="never" class="flex-1 mt-4 overflow-hidden">
            <template #header>
                <div class="flex justify-between items-center">
                    <div class="flex items-center space-x-4">
                        <span>出库明细</span>
                        <!-- <el-tag v-if="selectedOutOrder" :type="getStatusTagType(selectedOutOrder.status)">
                            {{ getStatusText(selectedOutOrder.status) }}
                        </el-tag> -->
                    </div>
                    <!-- <div v-if="selectedOutOrder" class="text-sm text-gray-500">
                        出库单号: {{ selectedOutOrder.orderSn }} | 出库时间: {{ parseTime(selectedOutOrder.outDate, '{y}-{m}-{d}') }}
                    </div> -->
                </div>
            </template>

            <div v-if="selectedOutOrder" class="h-full overflow-hidden flex flex-col">
                <el-table v-loading="detailItemLoading" :data="detailItemList" border height="100%" class="flex-1">
                    <el-table-column type="index" label="序号" width="60" />
                    <el-table-column label="物资信息" prop="resourceName" min-width="200">
                        <template #default="scope">
                            <div>
                                <div class="font-medium">{{ scope.row.resourceName }}</div>
                                <div class="text-sm text-gray-500">ID: {{ scope.row.resourceId }}</div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="物资型号" prop="specification" width="150" />
                    <el-table-column label="物资单位" prop="unit" width="100">
                        <template #default="scope">
                            <dict-tag :options="tnl_resource_unit" :value="scope.row.unit" />
                        </template>
                    </el-table-column>
                    <el-table-column label="物资性质" prop="nature" width="120">
                        <template #default="scope">
                            <dict-tag :options="tnl_resource_nature" :value="scope.row.nature" />
                        </template>
                    </el-table-column>
                    <el-table-column label="出库数量" prop="quantity" width="120" align="right">
                        <template #default="scope">
                            <el-tag type="warning">{{ scope.row.quantity }}</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="单价" prop="price" width="120" align="right">
                        <template #default="scope"> ¥{{ scope.row.price?.toFixed(2) || '0.00' }} </template>
                    </el-table-column>
                    <el-table-column label="金额" prop="amount" width="120" align="right">
                        <template #default="scope">
                            <span class="font-bold text-orange-500"> ¥{{ scope.row.amount?.toFixed(2) || '0.00' }} </span>
                        </template>
                    </el-table-column>
                    <el-table-column label="备注" prop="remark" min-width="150" show-overflow-tooltip />
                </el-table>

                <!-- 明细分页和统计 -->
                <div class="mt-4 space-y-4">
                    <!-- 统计信息 -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <div class="text-center">
                                    <div class="text-xl font-bold text-orange-600">{{ getTotalQuantity() }}</div>
                                    <div class="text-sm text-gray-500">总数量</div>
                                </div>
                            </el-col>
                            <el-col :span="8">
                                <div class="text-center">
                                    <div class="text-xl font-bold text-green-600">{{ detailItemList.length }}</div>
                                    <div class="text-sm text-gray-500">明细条数</div>
                                </div>
                            </el-col>
                            <el-col :span="8">
                                <div class="text-center">
                                    <div class="text-xl font-bold text-red-600">¥{{ getTotalAmount() }}</div>
                                    <div class="text-sm text-gray-500">总金额</div>
                                </div>
                            </el-col>
                        </el-row>
                    </div>

                    <!-- 明细分页 -->
                    <pagination
                        v-show="detailItemTotal > 0"
                        :total="detailItemTotal"
                        v-model:page="detailItemQueryParams.pageNum"
                        v-model:limit="detailItemQueryParams.pageSize"
                        @pagination="getDetailItemList"
                    />
                </div>
            </div>
        </el-card>
        <!-- 添加或修改出库单对话框 -->
        <el-dialog
            :title="isEditMode ? '修改出库单' : '添加出库单'"
            v-model="dialog.visible"
            width="1200px"
            append-to-body
            :close-on-click-modal="false"
            destroy-on-close
        >
            <el-form ref="resourceOutOrderFormRef" :model="form" :rules="rules" label-width="120px">
                <!-- 基本信息区域 -->
                <el-card shadow="never" class="mb-4">
                    <template #header>
                        <span>基本信息</span>
                    </template>
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item label="出库单号" prop="orderSn">
                                <el-input v-model="form.orderSn" placeholder="系统自动生成" readonly style="width: 100%" :disabled="true" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="出库类型" prop="stockOutType">
                                <el-select v-model="form.stockOutType" placeholder="请选择出库类型" style="width: 100%">
                                    <el-option v-for="dict in resource_outstock_way" :key="dict.value" :label="dict.label" :value="dict.value" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="出库时间" prop="outDate">
                                <el-date-picker
                                    v-model="form.outDate"
                                    type="date"
                                    placeholder="选择出库时间"
                                    style="width: 100%"
                                    value-format="YYYY-MM-DD"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="备注" prop="remark">
                                <el-input
                                    v-model="form.remark"
                                    type="textarea"
                                    :rows="2"
                                    placeholder="请输入备注信息"
                                    maxlength="500"
                                    show-word-limit
                                />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-card>
                <el-card shadow="never" class="mb-4">
                    <template #header>
                        <div style="display: flex; justify-content: space-between; align-items: center">
                            <span>出库明细</span>
                            <div>
                                <el-button type="primary" size="small" @click="handleAddItem"> 添加明细 </el-button>
                                <el-button type="danger" icon="Delete" @click="handleBatchDeleteItems"> 批量删除 </el-button>
                            </div>
                        </div>
                    </template>
                    <!-- 出库明细编辑区域 -->

                    <!-- 明细表格 -->
                    <el-table :data="outOrderItems" border height="300px" @selection-change="handleItemSelectionChange">
                        <el-table-column type="selection" width="55" align="center" />
                        <el-table-column type="index" label="序号" width="60" align="center" />

                        <el-table-column label="物资信息" min-width="250">
                            <template #default="scope">
                                <el-select
                                    :ref="(el) => setTableCellRef(el, scope.$index, 0)"
                                    v-model="scope.row.resourceId"
                                    placeholder="请输入物资名称搜索"
                                    filterable
                                    remote
                                    reserve-keyword
                                    :remote-method="(query) => searchResources(query, scope.$index)"
                                    :loading="resourceSearchLoading"
                                    @change="(value) => handleResourceChange(value, scope.$index)"
                                    @keydown.enter="handleCellKeyDown($event, scope.$index, 0)"
                                    style="width: 100%"
                                >
                                    <!-- 当前选中的物资信息（编辑模式下显示） -->
                                    <el-option
                                        v-if="scope.row.resourceName && scope.row.resourceId && isEditMode"
                                        :key="scope.row.resourceId"
                                        :label="scope.row.resourceName"
                                        :value="scope.row.resourceId"
                                    >
                                        <div>
                                            <div class="font-medium">{{ scope.row.resourceName }}</div>
                                            <div class="text-sm text-gray-500">
                                                规格: {{ scope.row.specification }} | 库存: {{ scope.row.balanceAmount }} {{ scope.row.unit }}
                                            </div>
                                        </div>
                                    </el-option>
                                    <!-- 搜索结果选项 -->
                                    <el-option
                                        v-for="resource in resourceOptions"
                                        :key="resource.id"
                                        :label="`${resource.typeName} - ${resource.specification}`"
                                        :value="resource.id"
                                    >
                                        <div>
                                            <div class="font-medium">{{ resource.typeName }}</div>
                                            <div class="text-sm text-gray-500">
                                                规格: {{ resource.specification }} | 库存: {{ resource.balanceAmount }} {{ resource.unit }}
                                            </div>
                                        </div>
                                    </el-option>
                                </el-select>
                            </template>
                        </el-table-column>

                        <el-table-column label="规格型号" prop="specification" width="150" />
                        <el-table-column label="单位" prop="unit" width="100" align="center">
                            <template #default="scope">
                                <dict-tag :options="tnl_resource_unit" :value="scope.row.unit" />
                            </template>
                        </el-table-column>
                        <el-table-column label="物资性质" prop="nature" width="120" align="center">
                            <template #default="scope">
                                <dict-tag :options="tnl_resource_nature" :value="scope.row.nature" />
                            </template>
                        </el-table-column>
                        <el-table-column label="库存数量" prop="balanceAmount" width="100" align="right">
                            <template #default="scope">
                                <el-tag type="info">{{ scope.row.balanceAmount || 0 }}</el-tag>
                            </template>
                        </el-table-column>

                        <el-table-column label="出库数量" width="120">
                            <template #default="scope">
                                <el-input-number
                                    :ref="(el) => setTableCellRef(el, scope.$index, 1)"
                                    v-model="scope.row.quantity"
                                    :min="0.01"
                                    :max="scope.row.balanceAmount || 999999"
                                    :precision="2"
                                    size="small"
                                    style="width: 100%"
                                    @change="(value) => handleQuantityChange(value, scope.$index)"
                                    @keydown.enter="handleCellKeyDown($event, scope.$index, 1)"
                                />
                            </template>
                        </el-table-column>

                        <el-table-column label="单价" width="120">
                            <template #default="scope">
                                <el-input-number
                                    :ref="(el) => setTableCellRef(el, scope.$index, 2)"
                                    v-model="scope.row.price"
                                    :min="0"
                                    :precision="2"
                                    size="small"
                                    style="width: 100%"
                                    @change="(value) => handlePriceChange(value, scope.$index)"
                                    @keydown.enter="handleCellKeyDown($event, scope.$index, 2)"
                                />
                            </template>
                        </el-table-column>

                        <el-table-column label="金额" prop="amount" width="120" align="right">
                            <template #default="scope">
                                <span class="font-bold text-orange-500"> ¥{{ (scope.row.quantity * scope.row.price || 0).toFixed(2) }} </span>
                            </template>
                        </el-table-column>

                        <el-table-column label="备注" width="150">
                            <template #default="scope">
                                <el-input
                                    :ref="(el) => setTableCellRef(el, scope.$index, 3)"
                                    v-model="scope.row.remark"
                                    placeholder="备注"
                                    size="small"
                                    @keydown.enter="handleCellKeyDown($event, scope.$index, 3)"
                                />
                            </template>
                        </el-table-column>

                        <el-table-column label="操作" width="80" align="center">
                            <template #default="scope">
                                <el-button link type="danger" icon="Delete" @click="handleDeleteItem(scope.$index)" />
                            </template>
                        </el-table-column>
                    </el-table>

                    <!-- 统计信息 -->
                    <div class="mt-4 p-4 bg-gray-50 rounded">
                        <el-row :gutter="20">
                            <el-col :span="6">
                                <div class="text-center">
                                    <div class="text-lg font-bold text-orange-600">{{ getFormTotalQuantity() }}</div>
                                    <div class="text-sm text-gray-500">总出库数量</div>
                                </div>
                            </el-col>
                            <el-col :span="6">
                                <div class="text-center">
                                    <div class="text-lg font-bold text-blue-600">{{ outOrderItems.length }}</div>
                                    <div class="text-sm text-gray-500">明细条数</div>
                                </div>
                            </el-col>
                            <el-col :span="6">
                                <div class="text-center">
                                    <div class="text-lg font-bold text-green-600">¥{{ getFormTotalAmount() }}</div>
                                    <div class="text-sm text-gray-500">总金额</div>
                                </div>
                            </el-col>
                            <el-col :span="6">
                                <div class="text-center">
                                    <div class="text-lg font-bold text-purple-600">¥{{ getFormAvgPrice() }}</div>
                                    <div class="text-sm text-gray-500">平均单价</div>
                                </div>
                            </el-col>
                        </el-row>
                    </div>
                </el-card>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button :loading="buttonLoading" type="primary" @click="submitForm">
                        {{ isEditMode ? '更 新' : '确 定' }}
                    </el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="ResourceOutOrder" lang="ts">
import { reactive, ref, toRefs, nextTick } from 'vue'
import {
    listResourceOutOrder,
    getResourceOutOrder,
    delResourceOutOrder,
    addResourceOutOrder,
    updateResourceOutOrder,
    confirmResourceOutOrder
} from '@/api/common/resourceOutOrder'
import { ResourceOutOrderVO, ResourceOutOrderQuery, ResourceOutOrderForm } from '@/api/common/resourceOutOrder/types'
import { getResourceOutOrderItemView } from '@/api/common/resourceOutOrderItem'
import { ResourceOutOrderItemViewVO } from '@/api/common/resourceOutOrderItem/types'
import { parseTime } from '@/utils/ruoyi'
import { listResource, getResourceView } from '@/api/common/resource'
import { ResourceViewVO, ResourceViewQuery } from '@/api/common/resource/types'

const { proxy } = getCurrentInstance() as ComponentInternalInstance

const resourceOutOrderList = ref<ResourceOutOrderVO[]>([])
const buttonLoading = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref<Array<string | number>>([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const dateRangeOutDate = ref<[DateModelType, DateModelType]>(['', ''])
// 主从表相关数据
const selectedOutOrderId = ref<string | number | null>(null)
const selectedOutOrder = ref<ResourceOutOrderVO | null>(null)

// 明细查看表相关数据
const detailItemList = ref<ResourceOutOrderItemViewVO[]>([])
const detailItemLoading = ref(false)
const detailItemTotal = ref(0)
const detailItemQueryParams = ref({
    pageNum: 1,
    pageSize: 10
})

// 编辑状态管理
const isEditMode = ref(false) // 是否为编辑模式
const editingOrderId = ref<string>('') // 正在编辑的出库单ID

// 表单明细编辑相关数据
const outOrderItems = ref<any[]>([])
const selectedItems = ref<any[]>([])

// 物资搜索相关
const resourceOptions = ref<any[]>([])
const resourceSearchLoading = ref(false)

// 表格单元格引用，用于键盘导航
const tableCellRefs = ref<any[][]>([])

// 设置表格单元格引用
const setTableCellRef = (el: any, rowIndex: number, colIndex: number) => {
    if (!tableCellRefs.value[rowIndex]) {
        tableCellRefs.value[rowIndex] = []
    }
    tableCellRefs.value[rowIndex][colIndex] = el
}

const queryFormRef = ref<ElFormInstance>()
const resourceOutOrderFormRef = ref<ElFormInstance>()

const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
})

const initFormData: ResourceOutOrderForm = {
    id: undefined,
    resourceId: undefined,
    orderSn: undefined,
    stockOutType: undefined,
    amount: undefined,
    remark: undefined,
    status: undefined,
    files: undefined,
    taskId: undefined,
    outDate: undefined
}
const data = reactive<PageData<ResourceOutOrderForm, ResourceOutOrderQuery>>({
    form: { ...initFormData },
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        resourceId: undefined,
        orderSn: undefined,
        stockOutType: undefined,
        status: undefined,
        params: {}
    },
    rules: {
        stockOutType: [{ required: true, message: '请选择出库类型', trigger: 'change' }],
        outDate: [{ required: true, message: '请选择出库时间', trigger: 'change' }]
    }
})

const { queryParams, form, rules } = toRefs(data)

// 字典数据
const { tnl_resource_unit, tnl_resource_nature, resource_outstock_way, resource_out_order_status } = toRefs<any>(
    proxy?.useDict('tnl_resource_unit', 'tnl_resource_nature', 'resource_outstock_way', 'resource_out_order_status')
)

/** 查询出库单列表 */
const getList = async () => {
    loading.value = true
    proxy?.addDateRange(queryParams.value, dateRangeOutDate.value, 'OutDate')
    const res = await listResourceOutOrder(queryParams.value)
    resourceOutOrderList.value = res.rows
    total.value = res.total
    loading.value = false
}

/** 取消按钮 */
const cancel = () => {
    reset()
    dialog.visible = false
}

/** 表单重置 */
const reset = () => {
    form.value = { ...initFormData }
    outOrderItems.value = []
    selectedItems.value = []
    resourceOptions.value = []
    tableCellRefs.value = []
    // 重置编辑状态
    isEditMode.value = false
    editingOrderId.value = ''
    resourceOutOrderFormRef.value?.resetFields()
}

/** 搜索按钮操作 */
const handleQuery = () => {
    queryParams.value.pageNum = 1
    getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
    dateRangeOutDate.value = ['', '']
    queryFormRef.value?.resetFields()
    handleQuery()
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: ResourceOutOrderVO[]) => {
    ids.value = selection.map((item) => item.id)
    single.value = selection.length != 1
    multiple.value = !selection.length
}

/** 生成出库单号 */
const generateOrderSn = () => {
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    const hour = String(now.getHours()).padStart(2, '0')
    const minute = String(now.getMinutes()).padStart(2, '0')
    const second = String(now.getSeconds()).padStart(2, '0')
    const millisecond = String(now.getMilliseconds()).padStart(3, '0')

    return `${year}${month}${day}-${hour}${minute}${second}-${millisecond}`
}

/** 获取编辑模式的出库明细 */
const getOutOrderItemsForEdit = async (orderId: string | number) => {
    try {
        // 使用现有的getResourceOutOrderItemView API
        const response = await getResourceOutOrderItemView(orderId)

        // 处理返回的明细数据
        const itemsData = Array.isArray(response.data) ? response.data : [response.data]

        // 清空现有明细
        outOrderItems.value = []

        // 转换明细数据为表单编辑格式
        for (const item of itemsData) {
            outOrderItems.value.push({
                resourceId: item.resourceId,
                resourceName: item.typeName, // 使用typeName
                specification: item.specification,
                unit: item.unit,
                nature: item.nature,
                balanceAmount: item.balanceAmount,
                quantity: item.quantity,
                price: item.price,
                amount: item.amount,
                remark: item.remark
            })
        }

        // 如果明细少于5行，补充空行到5行
        while (outOrderItems.value.length < 5) {
            outOrderItems.value.push({
                resourceId: '',
                resourceName: '',
                specification: '',
                unit: '',
                nature: '',
                balanceAmount: 0,
                quantity: 1,
                price: 0,
                amount: 0,
                remark: ''
            })
        }

        // 重新计算总计
        calculateTotalAmount()
    } catch (error) {
        console.error('获取出库明细失败:', error)
        proxy?.$modal.msgError('获取出库明细失败')
        // 如果获取失败，至少提供5个空行
        outOrderItems.value = []
        for (let i = 0; i < 5; i++) {
            outOrderItems.value.push({
                resourceId: '',
                resourceName: '',
                specification: '',
                unit: '',
                nature: '',
                balanceAmount: 0,
                quantity: 1,
                price: 0,
                amount: 0,
                remark: ''
            })
        }
    }
}
const handleViewTask = (id: string) => {
    proxy.$router.push('/subProject/plan/baseInfo?id=' + id + '&taskType=sealing&from=task')
}

/** 新增按钮操作 */
const handleAdd = () => {
    reset()
    isEditMode.value = false
    editingOrderId.value = ''

    form.value.orderSn = generateOrderSn()
    form.value.outDate = parseTime(new Date(), '{y}-{m}-{d}') // 默认今天
    outOrderItems.value = []
    selectedItems.value = []

    // 默认生成五行出库明细
    for (let i = 0; i < 5; i++) {
        outOrderItems.value.push({
            resourceId: '',
            resourceName: '',
            specification: '',
            unit: '',
            nature: '',
            balanceAmount: 0,
            quantity: 1,
            price: 0,
            amount: 0,
            remark: ''
        })
    }

    dialog.visible = true
    dialog.title = '添加出库单'
}

/** 修改按钮操作 */
const handleUpdate = async (row?: ResourceOutOrderVO) => {
    loading.value = true
    try {
        reset()
        isEditMode.value = true
        const _id = row?.id || ids.value[0]
        editingOrderId.value = _id.toString()

        // 获取出库单基本信息
        const res = await getResourceOutOrder(_id)
        Object.assign(form.value, res.data)

        // 获取出库明细列表
        await getOutOrderItemsForEdit(_id)

        dialog.visible = true
        dialog.title = '修改出库单'
    } catch (error) {
        console.error('获取出库单信息失败:', error)
        proxy?.$modal.msgError('获取出库单信息失败')
    } finally {
        loading.value = false
    }
}

/** 提交按钮 */
const submitForm = () => {
    // 验证基本信息
    resourceOutOrderFormRef.value?.validate(async (valid: boolean) => {
        if (!valid) return

        // 过滤掉未填写物资名称的行
        const validItems = outOrderItems.value.filter((item) => item.resourceId && item.resourceId !== '')

        // 验证明细数据
        if (validItems.length === 0) {
            proxy?.$modal.msgError('请至少添加一条出库明细')
            return
        }

        // 验证明细完整性
        for (let i = 0; i < validItems.length; i++) {
            const item = validItems[i]
            const originalIndex = outOrderItems.value.findIndex((originalItem) => originalItem === item)

            if (!item.quantity || item.quantity <= 0) {
                proxy?.$modal.msgError(`第${originalIndex + 1}行出库数量必须大于0`)
                return
            }
            if (item.quantity > item.balanceAmount) {
                proxy?.$modal.msgError(`第${originalIndex + 1}行出库数量不能超过库存数量`)
                return
            }
            if (item.price === undefined || item.price < 0) {
                proxy?.$modal.msgError(`第${originalIndex + 1}行请输入正确的单价`)
                return
            }
        }

        buttonLoading.value = true
        try {
            // 准备提交数据
            const submitData = {
                ...form.value,
                resourceOutOrderItems: validItems
            }

            if (isEditMode.value) {
                // 更新模式
                await updateResourceOutOrder(submitData)
                proxy?.$modal.msgSuccess('修改成功')
            } else {
                // 新增模式
                submitData.status = 'toconfirm'
                await addResourceOutOrder(submitData)
                proxy?.$modal.msgSuccess('新增成功')
            }
            dialog.visible = false
            await getList()
        } catch (error) {
            console.error('提交失败:', error)
            proxy?.$modal.msgError('操作失败')
        } finally {
            buttonLoading.value = false
        }
    })
}

/** 删除按钮操作 */
const handleDelete = async (row?: ResourceOutOrderVO) => {
    const _ids = row?.id || ids.value
    await proxy?.$modal.confirm('是否确认删除出库单编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false))
    await delResourceOutOrder(_ids)
    proxy?.$modal.msgSuccess('删除成功')
    await getList()
}

/** 确认出库按钮操作 */
const handleConfirmOutbound = async (row: ResourceOutOrderVO) => {
    await proxy?.$modal.confirm(`是否确认出库单号为"${row.orderSn}"的出库操作？确认后将更新物资库存。`)
    loading.value = true
    try {
        await confirmResourceOutOrder(row.id!)
        proxy?.$modal.msgSuccess('确认出库成功')
        await getList()
    } catch (error) {
        console.error('确认出库失败:', error)
        proxy?.$modal.msgError('确认出库失败')
    } finally {
        loading.value = false
    }
}

/** 主表行点击事件 */
const handleRowClick = (row: ResourceOutOrderVO) => {
    selectedOutOrderId.value = row.id
    selectedOutOrder.value = row
    detailItemQueryParams.value.pageNum = 1 // 重置到第一页
    getDetailItemList()
}

/** 获取出库明细列表 */
const getDetailItemList = async () => {
    if (!selectedOutOrderId.value) return

    detailItemLoading.value = true
    try {
        const response = await getResourceOutOrderItemView(selectedOutOrderId.value)

        // 处理API返回数据
        if (response.data) {
            detailItemList.value = Array.isArray(response.data) ? response.data : [response.data]
            detailItemTotal.value = detailItemList.value.length
        } else {
            detailItemList.value = []
            detailItemTotal.value = 0
        }
    } catch (error) {
        console.error('获取出库明细失败:', error)
        proxy?.$modal.msgError('获取出库明细失败')
        detailItemList.value = []
        detailItemTotal.value = 0
    } finally {
        detailItemLoading.value = false
    }
}

/** 获取行样式类名 */
const getRowClassName = ({ row }: { row: ResourceOutOrderVO }) => {
    return selectedOutOrderId.value === row.id ? 'selected-row' : ''
}

/** 统计方法 */
const getTotalQuantity = () => {
    return detailItemList.value.reduce((sum, item) => sum + (item.quantity || 0), 0)
}

const getTotalAmount = () => {
    const total = detailItemList.value.reduce((sum, item) => sum + (item.amount || 0), 0)
    return total.toFixed(2)
}

/** 状态相关方法 */
const getStatusTagType = (status: string) => {
    const statusMap = {
        'waiting': 'warning',
        'confirmed': 'success',
        'cancelled': 'danger'
    }
    return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
    const statusMap = {
        'waiting': '待出库',
        'confirmed': '已确认',
        'cancelled': '已取消'
    }
    return statusMap[status] || status
}

/** 表单明细管理方法 */
/** 添加明细行 */
const handleAddItem = () => {
    outOrderItems.value.push({
        resourceId: '',
        resourceName: '',
        specification: '',
        unit: '',
        balanceAmount: 0,
        quantity: 1,
        price: 0,
        amount: 0,
        remark: ''
    })
}

/** 删除明细行 */
const handleDeleteItem = (index: number) => {
    outOrderItems.value.splice(index, 1)
    calculateTotalAmount()
}

/** 批量删除明细 */
const handleBatchDeleteItems = () => {
    const selectedIds = selectedItems.value.map((item: any) => item.id)
    outOrderItems.value = outOrderItems.value.filter((item: any) => !selectedIds.includes(item.id))
    selectedItems.value = []
    calculateTotalAmount()
}

/** 明细选择变化 */
const handleItemSelectionChange = (selection: any[]) => {
    selectedItems.value = selection
}

/** 处理单元格键盘事件 */
const handleCellKeyDown = (event: KeyboardEvent, rowIndex: number, colIndex: number) => {
    event.preventDefault()

    // 计算下一个单元格位置
    let nextRowIndex = rowIndex
    let nextColIndex = colIndex + 1

    // 如果是最后一列，跳到下一行第一列
    if (nextColIndex > 3) {
        // 0:物资信息, 1:数量, 2:单价, 3:备注
        nextColIndex = 0
        nextRowIndex = rowIndex + 1

        // 如果是最后一行最后一列，自动添加新行
        if (nextRowIndex >= outOrderItems.value.length) {
            handleAddItem()
        }
    }

    // 聚焦到下一个单元格
    nextTick(() => {
        const nextCell = tableCellRefs.value[nextRowIndex]?.[nextColIndex]
        if (nextCell) {
            // 根据组件类型进行聚焦
            if (nextCell.focus) {
                nextCell.focus()
            } else if (nextCell.$el && nextCell.$el.querySelector) {
                const input = nextCell.$el.querySelector('input')
                if (input) {
                    input.focus()
                }
            }
        }
    })
}

/** 搜索物资 */
const searchResources = async (query: string, rowIndex?: number) => {
    if (!query || query.length < 2) {
        resourceOptions.value = []
        return
    }

    resourceSearchLoading.value = true
    try {
        // 使用物资搜索API
        const queryParams: ResourceViewQuery = {
            typeName: query,
            pageNum: 1,
            pageSize: 20
        }

        const response = await listResource(queryParams)

        // 过滤掉已经添加的物资(避免重复)
        const existingResourceIds = outOrderItems.value.map((item: any) => item.resourceId)
        resourceOptions.value = (response.rows || []).filter((resource: ResourceViewVO) => !existingResourceIds.includes(resource.id))
    } catch (error) {
        console.error('搜索物资失败:', error)
        resourceOptions.value = []
    } finally {
        resourceSearchLoading.value = false
    }
}

/** 物资选择变化 */
const handleResourceChange = async (resourceId: string | number, rowIndex: number) => {
    if (!resourceId) return

    try {
        // 获取物资详细信息
        const response = await getResourceView(resourceId)
        const resource = response.data

        // 更新明细行数据
        outOrderItems.value[rowIndex] = {
            ...outOrderItems.value[rowIndex],
            resourceId: resource.id,
            resourceName: resource.typeName,
            specification: resource.specification,
            unit: resource.unit,
            nature: resource.nature,
            balanceAmount: resource.balanceAmount,
            price: resource.price || 0,
            quantity: 1 // 默认数量为1
        }

        // 重新计算金额
        calculateItemAmount(rowIndex)
        calculateTotalAmount()
    } catch (error) {
        console.error('获取物资信息失败:', error)
        proxy?.$modal.msgError('获取物资信息失败')
    }
}

/** 数量变化处理 */
const handleQuantityChange = (value: number, rowIndex: number) => {
    outOrderItems.value[rowIndex].quantity = value
    calculateItemAmount(rowIndex)
    calculateTotalAmount()
}

/** 单价变化处理 */
const handlePriceChange = (value: number, rowIndex: number) => {
    outOrderItems.value[rowIndex].price = value
    calculateItemAmount(rowIndex)
    calculateTotalAmount()
}

/** 计算明细金额 */
const calculateItemAmount = (rowIndex: number) => {
    const item = outOrderItems.value[rowIndex]
    item.amount = (item.quantity || 0) * (item.price || 0)
}

/** 计算总金额 */
const calculateTotalAmount = () => {
    form.value.amount = outOrderItems.value.reduce((sum: number, item: any) => sum + (item.amount || 0), 0)
}

/** 表单统计方法 */
const getFormTotalQuantity = () => {
    return outOrderItems.value.reduce((sum: number, item: any) => sum + (item.quantity || 0), 0)
}

const getFormTotalAmount = () => {
    return (form.value.amount || 0).toFixed(2)
}

const getFormAvgPrice = () => {
    if (outOrderItems.value.length === 0) return '0.00'
    const totalPrice = outOrderItems.value.reduce((sum: number, item: any) => sum + (item.price || 0), 0)
    return (totalPrice / outOrderItems.value.length).toFixed(2)
}

/** 导出按钮操作 */
const handleExport = () => {
    proxy?.download(
        'common/resourceOutOrder/export',
        {
            ...queryParams.value
        },
        `resourceOutOrder_${new Date().getTime()}.xlsx`
    )
}

onMounted(() => {
    getList()
})
</script>

<style scoped lang="scss">
/* 页面整体布局 */
.h-full {
    height: 100%;
}

.flex-col {
    display: flex;
    flex-direction: column;
}

.flex-1 {
    flex: 1;
}

.flex-shrink-0 {
    flex-shrink: 0;
}

/* 选中行高亮 */
:deep(.selected-row) {
    background-color: #fef3c7 !important;
}

:deep(.selected-row:hover) {
    background-color: #fde68a !important;
}

/* 去掉表格行hover时的白色背景 */
:deep(.el-table__row:hover) {
    background-color: transparent !important;
}

:deep(.el-table__row:hover > td) {
    background-color: transparent !important;
}

/* 去掉表格行的默认背景 */
:deep(.el-table__row) {
    background-color: transparent !important;
}

:deep(.el-table__row > td) {
    background-color: transparent !important;
}

/* 去掉表格体的背景 */
:deep(.el-table__body-wrapper) {
    background-color: transparent !important;
}

/* 空状态样式 */
.text-gray-400 {
    color: #9ca3af;
}

.text-gray-500 {
    color: #6b7280;
}

/* 统计信息样式 */
.bg-gray-50 {
    background-color: transparent !important;
}

/* 出库特色样式 */
.text-orange-500 {
    color: #f97316;
}

.text-orange-600 {
    color: #ea580c;
}

.text-green-600 {
    color: #16a34a;
}

.text-red-600 {
    color: #dc2626;
}

.mb-4 {
    margin-bottom: 16px;
}

/* 出库数量标签样式 */
:deep(.el-tag--warning) {
    background-color: #fef3c7;
    border-color: #fbbf24;
    color: #92400e;
}

/* 确认出库按钮样式 */
:deep(.el-button--warning) {
    --el-button-text-color: #b45309;
    --el-button-hover-text-color: #92400e;
}

/* 去掉统计区域的背景 */
:deep(.p-4.rounded-lg) {
    background-color: transparent !important;
}

/* 响应式调整 */
@media (max-width: 1024px) {
    .el-card {
        margin-bottom: 16px;
    }

    /* 小屏幕时调整高度比例 */
    .el-card:first-of-type {
        height: 50vh !important;
        min-height: 300px !important;
    }
}

@media (max-width: 768px) {
    .el-col {
        margin-bottom: 10px;
    }

    /* 移动端时统计信息垂直排列 */
    .el-row .el-col {
        width: 100% !important;
        max-width: 100% !important;
    }
}
</style>
