import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { LifeCycleVO, LifeCycleForm, LifeCycleQuery, ChartDataResponse } from './types';

/**
 * 查询隧道全生命周期评价列表
 * @param query 查询参数
 * @returns
 */
export const listLifeCycle = (query: LifeCycleQuery) => {
  return request({
    url: '/subProject/lifeCycle/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询隧道全生命周期评价详细
 * @param id 主键
 * @returns
 */
export const getLifeCycle = (id: string | number): AxiosPromise<LifeCycleVO> => {
  return request({
    url: '/subProject/lifeCycle/' + id,
    method: 'get'
  });
};

/**
 * 新增隧道全生命周期评价
 * @param data 表单数据
 * @returns
 */
export const addLifeCycle = (data: LifeCycleForm) => {
  return request({
    url: '/subProject/lifeCycle',
    method: 'post',
    data: data
  });
};

/**
 * 修改隧道全生命周期评价
 * @param data 表单数据
 * @returns
 */
export const updateLifeCycle = (data: LifeCycleForm) => {
  return request({
    url: '/subProject/lifeCycle',
    method: 'put',
    data: data
  });
};

/**
 * 删除隧道全生命周期评价
 * @param id 主键
 * @returns
 */
export const delLifeCycle = (id: string | number | Array<string | number>) => {
  return request({
    url: '/subProject/lifeCycle/' + id,
    method: 'delete'
  });
};

/**
 * 获取图表数据（支持年月范围筛选）
 * @param projectId 项目ID
 * @param specialtyCode 专业类别代码
 * @param startYearMonth 开始年月
 * @param endYearMonth 结束年月
 * @returns
 */
export const getLifeCycleChartData = (
  projectId: string,
  specialtyCode: string,
  startYearMonth?: string,
  endYearMonth?: string
): AxiosPromise<ChartDataResponse> => {
  return request({
    url: '/subProject/lifeCycle/chart',
    method: 'get',
    params: {
      projectId,
      specialtyCode,
      startYearMonth,
      endYearMonth
    }
  });
};

/**
 * 获取项目最新分值数据
 * @param projectId 项目ID
 * @returns
 */
export const getLatestLifeCycleScore = (projectId: string): AxiosPromise<LifeCycleVO[]> => {
  return request({
    url: '/subProject/lifeCycle/latest',
    method: 'get',
    params: {
      projectId
    }
  });
};

/**
 * 根据专业类型获取数据项
 * @param specialtyCode 专业类型代码
 * @returns
 */
export const getDataItemsBySpecialty = (specialtyCode: string) => {
  return request({
    url: `/subProject/lifeCycle/dataItems/${specialtyCode}`,
    method: 'get'
  });
};

/**
 * 获取特定年月的详细评分数据
 * @param projectId 项目ID
 * @param specialtyCode 专业类型代码
 * @param year 年份
 * @param month 月份
 * @returns
 */
export const getDetailScores = (projectId: string, specialtyCode: string, year: number, month: number) => {
  return request({
    url: '/subProject/lifeCycle/detailScores',
    method: 'get',
    params: {
      projectId,
      specialtyCode,
      year,
      month
    }
  });
};

/**
 * 获取全部类别的平均值数据（支持年月范围筛选）
 * @param projectId 项目ID
 * @param startYearMonth 开始年月
 * @param endYearMonth 结束年月
 * @returns
 */
export const getAverageAllData = (
  projectId: string,
  startYearMonth?: string,
  endYearMonth?: string
): AxiosPromise<ChartDataResponse> => {
  return request({
    url: '/subProject/lifeCycle/averageAll',
    method: 'get',
    params: {
      projectId,
      startYearMonth,
      endYearMonth
    }
  });
};

/**
 * 获取全部类别在特定时间的雷达图数据
 * @param projectId 项目ID
 * @param year 年份
 * @param month 月份
 * @returns
 */
export const getRadarAllData = (
  projectId: string,
  year: number,
  month: number
) => {
  return request({
    url: '/subProject/lifeCycle/radarAll',
    method: 'get',
    params: {
      projectId,
      year,
      month
    }
  });
};

/**
 * 获取专业在时间范围内的最新数据
 * @param projectId 项目ID
 * @param specialtyCode 专业类别代码
 * @param startYearMonth 开始年月
 * @param endYearMonth 结束年月
 * @returns
 */
export const getLatestLifeCycleScoreInRange = (
  projectId: string,
  specialtyCode: string,
  startYearMonth?: string,
  endYearMonth?: string
): AxiosPromise<LifeCycleVO> => {
  return request({
    url: '/subProject/lifeCycle/latestInRange',
    method: 'get',
    params: {
      projectId,
      specialtyCode,
      startYearMonth,
      endYearMonth
    }
  });
};

/**
 * 获取全部类别在时间区间内的平均分雷达图数据
 * @param projectId 项目ID
 * @param startYearMonth 开始年月
 * @param endYearMonth 结束年月
 * @returns
 */
export const getRadarAllRangeData = (
  projectId: string,
  startYearMonth?: string,
  endYearMonth?: string
) => {
  return request({
    url: '/subProject/lifeCycle/radarAllRange',
    method: 'get',
    params: {
      projectId,
      startYearMonth,
      endYearMonth
    }
  });
};
