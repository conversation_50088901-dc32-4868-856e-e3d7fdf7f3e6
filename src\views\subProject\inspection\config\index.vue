<!-- 巡检配置列表 -->
<template>
    <div class="p-2 inspection-config-page">
        <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
            <div v-show="showSearch" class="mb-[10px]">
                <el-card shadow="hover">
                    <el-form ref="queryFormRef" :model="queryParams" :inline="true">
                        <el-form-item label="管理单元" prop="unitId">
                            <el-select v-model="queryParams.unitId" placeholder="请选择管理单元" clearable @change="handleUnitChange">
                                <el-option v-for="unit in manageUnits" :key="unit.id" :label="unit.name" :value="unit.id" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="房间" prop="roomId">
                            <el-select
                                v-model="queryParams.roomId"
                                placeholder="请选择房间"
                                clearable
                                :loading="roomsLoading"
                                :disabled="!queryParams.unitId"
                                @change="handleRoomChange"
                            >
                                <el-option v-for="room in rooms" :key="room.id" :label="room.roomNane" :value="room.id" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="设备分类" prop="categoryPath">
                            <el-cascader
                                v-model="queryParams.categoryPath"
                                :options="electricCategories"
                                :props="{
                                    value: 'path',
                                    label: 'name',
                                    children: 'children',
                                    emitPath: false,
                                    checkStrictly: true
                                }"
                                placeholder="请选择设备系统分类"
                                clearable
                                filterable
                                :loading="categoriesLoading"
                                @change="handleCategoryChange"
                            >
                                <template #default="{ data }">
                                    <span>{{ data.name }}</span>
                                    <span v-if="data.path" class="text-gray-400 text-xs ml-2">({{ data.path }})</span>
                                </template>
                            </el-cascader>
                        </el-form-item>
                        <el-form-item label="设备名称" prop="equipmentName">
                            <el-input
                                v-model="queryParams.equipmentName"
                                placeholder="请输入设备名称"
                                clearable
                                @keyup.enter="handleQuery"
                                @input="handleNameInput"
                            />
                        </el-form-item>
                        <el-form-item class="filter-actions">
                            <el-button type="primary" @click="handleQuery">搜索</el-button>
                            <el-button @click="resetQuery">重置</el-button>
                        </el-form-item>
                    </el-form>
                </el-card>
            </div>
        </transition>

        <el-card shadow="never">
            <template #header>
                <div class="btn-box">
                    <div class="filter">
                        <!-- <el-button type="primary" plain>待处理</el-button>
                        <el-button type="primary" plain>已处理</el-button>
                        <el-button type="primary" plain>全部</el-button> -->
                    </div>
                    <div class="export">
                        <el-button type="primary" plain @click="handleConfirm('选择设备')">选择设备</el-button>
                        <el-button plain @click="handleAssign()">巡检覆盖率</el-button>
                        <!-- <el-button type="primary" plain @click="handleConfirm('导出')">导出</el-button> -->
                    </div>
                </div>
            </template>

            <el-table v-loading="loading" stripe :data="configList" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="" align="center" prop="id" v-if="false" />
                <el-table-column label="设备名称" align="center" prop="equipmentName" />
                <el-table-column label="设备编码" align="center" prop="equipmentCode" />
                <el-table-column label="机电分系统" align="center">
                    <template #default="scope">
                        {{ getGrandParentEquipmentType(scope.row.categoryIdThird) }}
                    </template>
                </el-table-column>
                <!-- <el-table-column label="养护项目" align="center" prop="name" /> -->
                <el-table-column label="机电子系统" align="center">
                    <template #default="scope">
                        {{ getParentEquipmentType(scope.row.categoryIdThird) }}
                    </template>
                </el-table-column>
                <el-table-column label="设备类型" align="center" prop="categoryIdThird">
                    <template #default="scope">
                        {{ getEquipmentType(scope.row.categoryIdThird) }}
                    </template>
                </el-table-column>
                <el-table-column label="管理单元" align="center" prop="unitName">
                    <template #default="scope">
                        {{ scope.row.unitName || '-' }}
                    </template>
                </el-table-column>
                <el-table-column label="房间" align="center" prop="roomName">
                    <template #default="scope">
                        {{ scope.row.roomName || '-' }}
                    </template>
                </el-table-column>
                <el-table-column label="数据类型" align="center" prop="inpectionTypes">
                    <template #default="scope">
                        {{ getInspectTypesLabel(scope.row.inspectTypes) }}
                    </template>
                </el-table-column>
                <el-table-column label="配置巡检项" align="center" prop="configed">
                    <template #default="scope">
                        <el-tag :type="scope.row.configed == 1 ? 'success' : 'info'">
                            {{ scope.row.configed == 1 ? '已配置' : '未配置' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
                    <template #default="scope">
                        <div class="op-actions">
                            <el-button link type="primary" class="op-link op-edit" @click="handleConfig(scope.row)">
                                <img class="op-icon" src="@/assets/images/edit-icon.png" alt="巡检项" />
                                巡检项
                            </el-button>
                            <el-button link type="danger" class="op-link op-delete" @click="handleDelete(scope.row)">
                                <img class="op-icon" src="@/assets/images/delete-icon.png" alt="删除" />
                                删除
                            </el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>

            <pagination
                v-show="total > 0"
                :total="total"
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                @pagination="getList"
            />
        </el-card>
        <!-- 设备选择弹出框组件 -->
        <EquipmentSelect
            v-model:visible="dialog.visible"
            :title="dialog.title"
            :manage-units="manageUnits"
            :button-loading="buttonLoading"
            @confirm="handleEquipmentConfirm"
            @cancel="handleEquipmentCancel"
        />
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, toRefs, onMounted, getCurrentInstance, ComponentInternalInstance } from 'vue'
import { listInspectionConfigView, delInspectionConfig } from '@/api/subProject/inspection/inspectionConfig'
import { InspectionConfigViewQuery, InspectionConfigViewVO, InspectionConfigForm } from '@/api/subProject/inspection/inspectionConfig/types'
import { listProjectManageUnit } from '@/api/project/manageUnit'
import { ManageUnitVO } from '@/api/project/manageUnit/types'
import { listRoom } from '@/api/project/room'
import { RoomVO } from '@/api/project/room/types'
import EquipmentSelect from './EquipmentSelect.vue'
import { useAppStore } from '@/store/modules/app'
import { useRouter } from 'vue-router'
import { listCategory } from '@/api/common/category'
import { CategoryVO } from '@/api/common/category/types'

const { proxy } = getCurrentInstance() as ComponentInternalInstance
const router = useRouter()
const appStore = useAppStore()

// 获取数据字典
const { inspect_type } = toRefs<any>(proxy?.useDict('inspect_type'))

// 响应式数据定义
const configList = ref<InspectionConfigViewVO[]>([])
const manageUnits = ref<ManageUnitVO[]>([])
const rooms = ref<RoomVO[]>([])
const equipmentTypes = ref<CategoryVO[]>([])
const electricCategories = ref<CategoryVO[]>([])
const buttonLoading = ref(false)
const loading = ref(true)
const roomsLoading = ref(false)
const categoriesLoading = ref(false)
const showSearch = ref(true)
const ids = ref<Array<string | number>>([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)

const queryFormRef = ref<ElFormInstance>()

const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
})

const queryParams = reactive<InspectionConfigViewQuery>({
    pageNum: 1,
    pageSize: 10,
    projectId: appStore.projectContext.selectedProjectId,
    name: '',
    categoryPath: '',
    roomId: undefined,
    unitId: undefined,
    params: {}
})

/** 查询年度计划目录列表 */
const getList = async () => {
    loading.value = true
    try {
        // 构建查询参数
        const searchParams = {
            ...queryParams,
            // 设备名称模糊查询
            equipmentName: queryParams.equipmentName?.trim() || undefined
        }

        console.log('查询参数:', searchParams)
        console.log('分类路径匹配:', searchParams.categoryPath)

        const res = await listInspectionConfigView(searchParams)
        if (res) {
            // 处理分页响应数据
            configList.value = res.rows || []
            total.value = res.total || 0

            console.log(`查询结果: 找到${configList.value.length}个设备，总计${total.value}个`)
            if (searchParams.categoryPath) {
                console.log(`包含路径"${searchParams.categoryPath}"开头的所有设备`)
            }
        } else {
            configList.value = []
            total.value = 0
        }
    } catch (error) {
        console.error('获取配置列表失败:', error)
        configList.value = []
        total.value = 0
    } finally {
        loading.value = false
    }
}

/** 获取当前项目的所有管理单元 */
const getManageUnits = async () => {
    try {
        const projectId = appStore.projectContext.selectedProjectId
        console.log('获取管理单元参数 - projectId:', projectId)

        const res = await listProjectManageUnit(projectId)
        console.log('API返回的管理单元数据:', res)

        manageUnits.value = Array.isArray(res) ? res : res.data || res.rows || []
        console.log('解析后的管理单元列表:', manageUnits.value)

        if (manageUnits.value.length === 0) {
            console.warn('没有获取到管理单元数据')
        } else {
            console.log(`成功获取 ${manageUnits.value.length} 个管理单元`)
        }
    } catch (error) {
        console.error('获取管理单元列表失败:', error)
        manageUnits.value = []
    }
}

/** 根据管理单元ID查询房间列表 */
const getRoomsByUnitId = async (unitId: string | number) => {
    if (!unitId) {
        rooms.value = []
        return
    }

    try {
        roomsLoading.value = true
        console.log('开始获取房间列表，unitId:', unitId)
        const response = await listRoom({
            unitId: unitId,
            pageNum: 1,
            pageSize: 1000 // 获取所有房间
        })
        rooms.value = response.rows || []
        console.log(`获取到${rooms.value.length}个房间`)
    } catch (error) {
        console.error('获取房间列表失败:', error)
        proxy?.$modal.msgError('获取房间列表失败')
        rooms.value = []
    } finally {
        roomsLoading.value = false
    }
}

/** 获取electric专业的设备分类 */
const getElectricCategories = async () => {
    try {
        categoriesLoading.value = true
        console.log('开始获取electric专业的设备分类')

        const response = await listCategory({
            kind: 'equipment',
            specialty: 'electric' // 筛选electric专业
        })

        // 构建树形结构供选择器使用
        electricCategories.value = buildCategoryTree(response.data || [])
        console.log(`获取到${electricCategories.value.length}个electric设备分类`)
    } catch (error) {
        console.error('获取electric设备分类失败:', error)
        proxy?.$modal.msgError('获取设备分类失败')
        electricCategories.value = []
    } finally {
        categoriesLoading.value = false
    }
}

// 构建分类树形结构
const buildCategoryTree = (categories: CategoryVO[]) => {
    const categoryMap = new Map()
    const rootCategories = []

    // 第一遍遍历，创建所有节点
    categories.forEach((category) => {
        categoryMap.set(category.id, {
            ...category,
            children: [],
            // 确保有path字段，如果没有则构建
            path: category.path || buildCategoryPath(category, categories)
        })
    })

    // 第二遍遍历，建立父子关系
    categories.forEach((category) => {
        const node = categoryMap.get(category.id)
        if (category.parentId && categoryMap.has(category.parentId)) {
            categoryMap.get(category.parentId).children.push(node)
        } else {
            rootCategories.push(node)
        }
    })

    return rootCategories
}

// 构建分类路径（如果API没有返回path字段）
const buildCategoryPath = (category: CategoryVO, allCategories: CategoryVO[]) => {
    const paths = []
    let current = category

    while (current) {
        paths.unshift(current.id)
        if (current.parentId) {
            current = allCategories.find((c) => c.id === current.parentId)
        } else {
            break
        }
    }

    return paths.join('/')
}

/** 获取设备类型列表 */
const getEquipmentTypes = async () => {
    try {
        const res = await listCategory({
            kind: 'equipment'
        })
        console.log('获取到的设备类型数据:', res)
        equipmentTypes.value = res.data || []
        console.log('设置后的equipmentTypes:', equipmentTypes.value)
    } catch (error) {
        console.error('获取设备类型列表失败:', error)
        equipmentTypes.value = []
    }
}

const getParentEquipmentType = (categoryId: string) => {
    if (!categoryId) return '-'
    const currentType = equipmentTypes.value.find((type) => type.id === categoryId)
    if (!currentType) return '-'
    const parentType = equipmentTypes.value.find((type) => type.id === currentType.parentId)
    return parentType?.name || '-'
}

const getGrandParentEquipmentType = (categoryId: string) => {
    if (!categoryId) return '-'
    const currentType = equipmentTypes.value.find((type) => type.id === categoryId)
    if (!currentType) return '-'
    const parentType = equipmentTypes.value.find((type) => type.id === currentType.parentId)
    if (!parentType) return '-'
    const grandParentType = equipmentTypes.value.find((type) => type.id === parentType.parentId)
    return grandParentType?.name || '-'
}

/** 获取巡检类型标题 */
const getInspectTypesLabel = (inspectTypes: string) => {
    if (!inspectTypes) return '-'

    // 将逗号分隔的字符串转换为数组
    const typeArray = inspectTypes
        .split(',')
        .map((type) => type.trim())
        .filter((type) => type.length > 0)

    // 查找每个类型对应的标题
    const labels = typeArray.map((typeValue) => {
        const dictItem = inspect_type.value?.find((item: any) => item.value === typeValue)
        return dictItem ? dictItem.label : typeValue
    })

    // 用逗号连接所有标题
    return labels.join(', ')
}

/** 设备选择确认 */
const handleEquipmentConfirm = async (result: { success: boolean; result: any; queryConditions: any }) => {
    console.log('设备巡检配置完成:', result)

    try {
        // 自动刷新列表
        console.log('巡检配置成功，开始刷新巡检配置列表...')
        await getList()
        console.log('巡检配置列表刷新完成')
    } catch (error) {
        console.error('刷新列表失败:', error)
        proxy?.$modal.msgError('巡检配置成功，但刷新列表失败，请手动刷新页面')
    }
}

/** 设备选择取消 */
const handleEquipmentCancel = () => {
    dialog.visible = false
}

/** 管理单元变化处理 */
const handleUnitChange = async (unitId: string | number | undefined) => {
    console.log('管理单元变化:', unitId)

    // 清空房间选择
    queryParams.roomId = undefined

    if (unitId) {
        // 动态加载该管理单元下的房间
        await getRoomsByUnitId(unitId)
    } else {
        // 清空房间列表
        rooms.value = []
    }

    // 触发查询
    handleQuery()
}

/** 房间变化处理 */
const handleRoomChange = () => {
    console.log('房间变化:', queryParams.roomId)
    handleQuery()
}

/** 设备分类变化处理 */
const handleCategoryChange = (selectedPath: string | undefined) => {
    console.log('设备分类变化:', selectedPath)

    if (selectedPath) {
        // 获取选中节点的完整路径
        const selectedCategory = findCategoryByPath(selectedPath)
        queryParams.categoryPath = selectedCategory?.path || selectedPath

        console.log('设置查询路径为:', queryParams.categoryPath)
        console.log('将查询该路径及其所有子路径的设备')
    } else {
        queryParams.categoryPath = undefined
    }

    handleQuery()
}

// 根据路径查找分类节点
const findCategoryByPath = (targetPath: string): CategoryVO | null => {
    const findInTree = (categories: CategoryVO[]): CategoryVO | null => {
        for (const category of categories) {
            if (category.path === targetPath || category.id === targetPath) {
                return category
            }
            if (category.children && category.children.length > 0) {
                const found = findInTree(category.children)
                if (found) return found
            }
        }
        return null
    }

    return findInTree(electricCategories.value)
}

/** 设备名称输入处理 */
const handleNameInput = () => {
    console.log('设备名称查询:', queryParams.equipmentName)
    handleQuery()
}

/** 搜索按钮操作 */
const handleQuery = () => {
    queryParams.pageNum = 1
    getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
    queryFormRef.value?.resetFields()
    queryParams.roomId = undefined // 清空房间选择
    queryParams.categoryPath = undefined // 清空分类路径选择
    queryParams.equipmentName = undefined
    rooms.value = [] // 清空房间列表
    handleQuery()
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: InspectionConfigViewVO[]) => {
    ids.value = selection.map((item) => item.id)
    single.value = selection.length != 1
    multiple.value = !selection.length
}

/** 导航到查看、指派页面 */
const handleAssign = () => {
    router.push('coverageRate')
}

/** 审批按钮操作 */
const handleConfirm = (operationName: string) => {
    dialog.visible = true
    dialog.title = operationName
}

//配置巡检项
const handleConfig = (row: InspectionConfigViewVO) => {
    console.log('配置巡检项:', row)

    router.push({
        path: 'configItems',
        query: {
            deviceId: row.equpimentId,
            categoryId: row.categoryIdThird
        }
    })
}

/** 删除按钮操作 */
const handleDelete = async (row?: InspectionConfigViewVO) => {
    if (!row?.id) {
        proxy?.$modal.msgWarning('请选择要删除的巡检配置')
        return
    }

    try {
        await proxy?.$modal.confirm(`是否确认该设备的巡检配置？`)

        console.log('开始删除巡检配置，ID:', row.id)
        await delInspectionConfig(row.id)

        proxy?.$modal.msgSuccess('删除成功')
        console.log('删除成功，刷新列表')

        // 刷新列表
        await getList()
    } catch (error) {
        if (error !== 'cancel') {
            // 用户取消操作不显示错误
            console.error('删除巡检配置失败:', error)
            proxy?.$modal.msgError('删除失败，请重试')
        }
    }
}

/**
 * 获取设备类型名称
 * @param categoryId 设备类型ID
 * @returns 设备类型名称，如果未找到则返回 '-'
 */
const getEquipmentType = (categoryId: string) => {
    console.log('获取设备类型名称，categoryId:', categoryId)
    console.log('当前equipmentTypes:', equipmentTypes.value)
    const type = equipmentTypes.value.find((type) => type.id == categoryId)
    console.log('找到的设备类型:', type)
    return type?.name || '-'
}

onMounted(async () => {
    try {
        await Promise.all([
            getList(),
            getManageUnits(),
            getEquipmentTypes(),
            getElectricCategories() // 新增：获取electric分类
        ])

        // 如果有默认的unitId，则加载对应的房间
        if (queryParams.unitId) {
            await getRoomsByUnitId(queryParams.unitId)
        }
    } catch (error) {
        console.error('初始化数据失败:', error)
    }
})
</script>
<style lang="scss" scoped>
.inspection-config-page {

    /* 头部卡片与分隔 */
    :deep(.el-card) {
        background: transparent !important;
        border: none !important;
        box-shadow: none !important;
    }

    :deep(.el-card__header) {
        border-bottom: none !important;
        padding-top: 0 !important;
    }

    :deep(.el-card__body) {
        background: transparent !important;
        padding-bottom: 0 !important;
    }

    /* 输入/下拉统一深色皮肤 */
    :deep(.el-input__wrapper),
    :deep(.el-select__wrapper),
    :deep(.el-cascader .el-input__wrapper) {
        background: #232d45 !important;
        border-radius: 6px !important;
        box-shadow: none !important;
        min-height: 36px;
        height: 36px;
        padding: 5px 10px;
    }

    :deep(.el-input__inner::placeholder),
    :deep(.el-select__placeholder),
    :deep(.el-cascader .el-input__inner::placeholder) {
        color: #8291A9 !important;
        opacity: 1;
    }

    :deep(.el-input__inner),
    :deep(.el-select .el-select__selected-item > span:not(.el-select__placeholder)),
    :deep(.el-cascader .el-input__inner) {
        color: #FFFFFF !important;
    }

    :deep(.el-select__wrapper.is-focused),
    :deep(.el-cascader .el-input__wrapper.is-focus) {
        box-shadow: none !important;
    }

    /* 级联选择器特殊样式 */
    :deep(.el-cascader) {
        width: 100%;
    }

    :deep(.el-cascader .el-input) {
        background: #232d45 !important;
        border-radius: 6px !important;
        border: none !important;
    }

    /* 搜索/重置按钮 */
    :deep(.filter-actions .el-button:not(.is-link)) {
        border-radius: 6px !important;
        height: 40px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        font-size: 14px !important;
        margin-right: 10px;
    }

    :deep(.filter-actions .el-button.el-button--primary:not(.is-link)) {
        background-color: #4286F3 !important;
        border-color: #4286F3 !important;
        color: #FFFFFF !important;
    }

    :deep(.filter-actions .el-button.el-button--primary:not(.is-link))::before {
        content: "";
        display: inline-block;
        width: 16px;
        height: 16px;
        background: url('@/assets/images/search-icon.png') no-repeat center/contain;
        margin-right: 6px;
    }

    :deep(.filter-actions .el-button:not(.el-button--primary):not(.is-link)) {
        background-color: #808892 !important;
        border-color: #808892 !important;
        color: #FFFFFF !important;
    }

    :deep(.filter-actions .el-button:not(.el-button--primary):not(.is-link))::before {
        content: "";
        display: inline-block;
        width: 16px;
        height: 16px;
        background: url('@/assets/images/restart-icon.png') no-repeat center/contain;
        margin-right: 6px;
    }

    /* 表格透明化与去边框 */
    :deep(.el-table) {
        --el-table-bg-color: transparent;
        --el-table-header-bg-color: transparent;
        --el-table-tr-bg-color: transparent;
        --el-table-border-color: rgba(255, 255, 255, 0.08);
        background-color: transparent !important;
        color: #ffffff;
    }

    :deep(.el-table__inner-wrapper::before),
    :deep(.el-table__inner-wrapper::after),
    :deep(.el-table::before),
    :deep(.el-table--border::after),
    :deep(.el-table__border-left-patch) {
        display: none !important;
        background: transparent !important;
    }

    :deep(.el-table__header th) {
        background-color: transparent !important;
        border-bottom: none !important;
        height: 44px;
        text-align: center;
    }

    :deep(.el-table__header th .cell) {
        color: #AED7F2 !important;
        font-size: 14px !important;
        font-weight: 500 !important;
        text-align: center;
        position: relative;
        top: -3px;
    }

    :deep(.el-table td) {
        text-align: center !important;
        height: 48px;
        background-color: transparent !important;
    }

    :deep(.el-table .cell) {
        color: #FFFFFF !important;
        font-size: 13px !important;
        line-height: 1.4;
        padding: 8px 12px;
    }

    /* 整行隔行渐变 */
    :deep(.el-table__body tr) {
        background: transparent !important;
    }

    :deep(.el-table--striped .el-table__body tr.el-table__row--striped) {
        background: linear-gradient(to right, rgba(79, 158, 249, 0.02) 0%, rgba(79, 158, 249, 0.12) 60%, rgba(79, 158, 249, 0.22) 100%) !important;
    }

    :deep(.el-table__body tr:hover > td) {
        background-color: rgba(66, 134, 243, 0.08) !important;
    }

    /* 表头整行背景 */
    :deep(.el-table__header-wrapper) {
        position: relative;
    }

    :deep(.el-table__header-wrapper)::before {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        height: 44px;
        background: rgb(31, 43, 78);
        border-radius: 6px;
        pointer-events: none;
        z-index: 0;
    }

    :deep(.el-table thead),
    :deep(.el-table th.el-table__cell) {
        position: relative;
        z-index: 1;
    }

    /* 表体每行间隔线 */
    :deep(.el-table__body tr > td) {
        border-bottom: 1px solid rgba(255, 255, 255, 0.06) !important;
    }

    /* 筛选按钮组样式 */
    .btn-box {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .filter {
            flex: 1;
        }

        .export {
            margin-left: auto;
        }
    }

    /* 操作列图标按钮 */
    .op-actions {
        display: flex;
        align-items: center;
        gap: 8px;
        justify-content: center;
        padding-right: 8px;
    }

    .op-link {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        padding: 0 6px;
    }

    .op-icon {
        width: 14px;
        height: 14px;
        display: inline-block;
        margin-right: 4px;
    }

    .op-info {
        color: #4286F3 !important;
    }

    .op-edit {
        color: #42F3E9 !important;
    }

    .op-delete {
        color: #D62121 !important;
    }
}
</style>
