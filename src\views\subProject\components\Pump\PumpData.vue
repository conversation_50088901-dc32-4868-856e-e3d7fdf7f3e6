<!-- 泵房展示组件 -->
<template>
    <div class="pump-data-container">
        <!-- 页面头部 -->
        <div class="header-section">
            <div class="title-area">
                <h2>泵房液位监测</h2>
                <el-tag type="info" size="small">实时液位数据</el-tag>
            </div>

            <!-- 查询控件 -->
            <div class="query-section">
                <el-form :inline="true" class="demo-form-inline">
                    <el-form-item label="泵房筛选" v-if="false">
                        <el-select v-model="selectedPump" placeholder="选择泵房" @change="updateChart" style="width: 200px">
                            <el-option label="全部泵房" value="" />
                            <el-option v-for="pump in pumpOptions" :key="pump.name" :label="pump.assetName" :value="pump.name" />
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button @click="handleRefresh">
                            <el-icon><Refresh /></el-icon>
                            刷新
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-cards" v-if="!loading && statsData">
            <el-row :gutter="20">
                <el-col :span="6">
                    <el-card shadow="hover">
                        <div class="stat-item">
                            <div class="stat-value">{{ statsData.totalPumps }}</div>
                            <div class="stat-label">泵房总数</div>
                        </div>
                    </el-card>
                </el-col>
                <el-col :span="6">
                    <el-card shadow="hover">
                        <div class="stat-item">
                            <div class="stat-value">{{ statsData.totalPoints }}</div>
                            <div class="stat-label">监测点总数</div>
                        </div>
                    </el-card>
                </el-col>
                <el-col :span="6">
                    <el-card shadow="hover">
                        <div class="stat-item">
                            <div class="stat-value">{{ statsData.normalPoints }}</div>
                            <div class="stat-label">正常监测点</div>
                        </div>
                    </el-card>
                </el-col>
                <el-col :span="6">
                    <el-card shadow="hover">
                        <div class="stat-item">
                            <div class="stat-value">{{ statsData.avgLevel.toFixed(1) }}mm</div>
                            <div class="stat-label">平均液位</div>
                        </div>
                    </el-card>
                </el-col>
            </el-row>
        </div>

        <!-- 主要内容区域 -->
        <div class="content-section">
            <!-- 图表区域 -->
            <el-card shadow="never" class="chart-card">
                <template #header>
                    <div class="card-header">
                        <span>液位趋势图</span>
                        <div class="header-actions">
                            <el-tag :type="selectedPump ? 'primary' : 'info'" size="small">
                                {{ selectedPump ? '单泵房模式' : '多泵房模式' }}
                            </el-tag>
                        </div>
                    </div>
                </template>

                <div v-loading="loading" class="chart-container">
                    <div v-if="!loading && filteredData.length === 0" class="empty-state">
                        <el-empty description="暂无泵房数据" />
                    </div>
                    <div v-else ref="chartRef" class="chart" style="height: 400px"></div>
                </div>
            </el-card>

            <!-- 数据列表区域 -->
            <el-card shadow="never" class="table-card">
                <template #header>
                    <div class="card-header">
                        <span>泵房详细信息</span>
                        <el-button type="text" @click="toggleTableCollapse">
                            {{ isTableCollapsed ? '展开' : '折叠' }}
                            <el-icon><ArrowDown v-if="isTableCollapsed" /><ArrowUp v-else /></el-icon>
                        </el-button>
                    </div>
                </template>

                <el-collapse-transition>
                    <div v-show="!isTableCollapsed">
                        <el-table :data="filteredTableData" v-loading="loading" height="400" stripe border>
                            <el-table-column prop="pumpName" label="泵房名称" width="180" fixed="left" />
                            <el-table-column prop="pointIndex" label="监测点编号" width="100" />
                            <el-table-column prop="pointValue" label="当前液位" width="120" sortable>
                                <template #default="{ row }">
                                    <span :class="['level-value', `level-${row.status}`]"> {{ row.pointValue }}mm </span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="statusText" label="状态" width="100">
                                <template #default="{ row }">
                                    <el-tag
                                        :type="row.status === 'normal' ? 'success' : row.status === 'warning' ? 'warning' : 'danger'"
                                        size="small"
                                    >
                                        {{ row.statusText }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column prop="lowerLine" label="下限" width="100" />
                            <el-table-column prop="higherLine" label="上限" width="100" />
                            <el-table-column prop="maxLine" label="最大液位" width="120" />
                            <el-table-column prop="position" label="位置" width="200" />
                            <el-table-column prop="tunnelId" label="隧道ID" width="100" />
                        </el-table>
                    </div>
                </el-collapse-transition>
            </el-card>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, ArrowDown, ArrowUp } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { getPumpLevel } from '@/api/subProject/health/pump/index'
import type { PumpDeviceData } from '@/api/subProject/health/pump/types'

// 组件属性
interface Props {
    pumpName?: string // 用于筛选显示的泵房
    tunnelId?: string // 隧道ID筛选
}
const props = withDefaults(defineProps<Props>(), {
    pumpName: '',
    tunnelId: ''
})

// 响应式数据
const loading = ref(false)
const pumpData = ref<PumpDeviceData[]>([])
const selectedPump = ref('')
const isTableCollapsed = ref(true) // 默认折叠
const chartRef = ref<HTMLElement>()
let chartInstance: echarts.ECharts | null = null

// 定时器
let refreshTimer: ReturnType<typeof setInterval>

// 泵房选项
const pumpOptions = computed(() => {
    return pumpData.value.map((pump) => ({
        name: pump.name,
        assetName: pump.assetName
    }))
})

// 统计数据
const statsData = computed(() => {
    if (!pumpData.value.length) return null

    let totalPoints = 0
    let normalPoints = 0
    let warningPoints = 0
    let totalLevel = 0
    let levelCount = 0

    pumpData.value.forEach((pump) => {
        // 统计监测点数据
        pump.info.forEach((point) => {
            totalPoints++
            const value = point.value

            if (!isNaN(value) && value > 0) {
                totalLevel += value
                levelCount++
            }

            // 判断监测点状态（这里可以根据业务需求调整判断逻辑）
            if (value >= pump.lowerLine && value <= pump.higherLine) {
                normalPoints++
            } else if (value > 0) {
                warningPoints++
            }
        })
    })

    return {
        totalPumps: pumpData.value.length,
        totalPoints: totalPoints,
        normalPoints: normalPoints,
        warningPoints: warningPoints,
        avgLevel: levelCount > 0 ? totalLevel / levelCount : 0
    }
})

// 过滤的数据（根据选择的泵房）
const filteredData = computed(() => {
    if (!selectedPump.value) return pumpData.value
    return pumpData.value.filter((pump) => pump.name === selectedPump.value)
})

// 表格数据 - 显示监测点信息
const filteredTableData = computed(() => {
    const tableData: any[] = []

    filteredData.value.forEach((pump) => {
        // 为每个监测点创建一行数据
        pump.info.forEach((point) => {
            const value = point.value
            const isNormal = value >= pump.lowerLine && value <= pump.higherLine
            const isWarning = value > pump.higherLine
            const isDanger = value < pump.superLowLine

            tableData.push({
                pumpName: pump.assetName,
                pointIndex: point.index,
                pointValue: value.toFixed(1),
                status: isDanger ? 'danger' : isWarning ? 'warning' : value > 0 ? 'normal' : 'offline',
                statusText: isDanger ? '超低液位' : isWarning ? '高液位' : value > 0 ? '正常' : '离线',
                lowerLine: pump.lowerLine,
                higherLine: pump.higherLine,
                maxLine: pump.maxLine,
                position: pump.position,
                tunnelId: pump.tunnelId
            })
        })
    })

    return tableData
})

// 获取泵房数据
const fetchData = async () => {
    try {
        loading.value = true

        console.log('=== PumpData 组件调试信息 ===')
        console.log('Props 数据:', props)
        console.log('pumpName:', props.pumpName)
        console.log('tunnelId:', props.tunnelId)

        const query: any = {}
        if (props.pumpName) query.name = props.pumpName
        if (props.tunnelId) query.tunnelId = props.tunnelId

        console.log('构建的查询参数:', query)
        console.log('查询参数数量:', Object.keys(query).length)
        console.log('最终传递给API的参数:', Object.keys(query).length > 0 ? query : undefined)
        console.log('============================')

        const response = await getPumpLevel(Object.keys(query).length > 0 ? query : undefined)

        console.log('泵房液位数据', response)
        if (response.data && response.data.results) {
            pumpData.value = response.data.results
            await nextTick()
            updateChart()
        } else {
            pumpData.value = []
            ElMessage.warning('暂无泵房数据')
        }
    } catch (error) {
        console.error('获取泵房数据失败:', error)
        ElMessage.error('获取泵房数据失败')
        pumpData.value = []
    } finally {
        loading.value = false
    }
}

// 更新图表
const updateChart = async () => {
    if (!chartRef.value || !filteredData.value.length) return

    await nextTick()

    if (!chartInstance) {
        chartInstance = echarts.init(chartRef.value)
    }

    // 准备图表数据 - 使用监测点数据
    const series: any[] = []
    const colors = ['#409eff', '#67c23a', '#e6a23c', '#f56c6c', '#909399']

    filteredData.value.forEach((pump, pumpIndex) => {
        const color = colors[pumpIndex % colors.length]

        // 为每个监测点创建数据系列
        pump.info.forEach((point, pointIndex) => {
            if (point.value > 0) {
                // 只显示有数据的监测点
                series.push({
                    name: `${pump.assetName}-${point.index}`,
                    type: 'bar',
                    data: [point.value],
                    itemStyle: { color },
                    barWidth: 20,
                    label: {
                        show: true,
                        position: 'top',
                        formatter: `${point.value}mm`
                    }
                })
            }
        })

        // 添加上下限线
        series.push({
            name: `${pump.assetName}-上限`,
            type: 'line',
            data: [pump.higherLine],
            smooth: false,
            symbol: 'none',
            lineStyle: {
                color: '#f56c6c',
                type: 'dashed',
                width: 2
            },
            showSymbol: false
        })

        series.push({
            name: `${pump.assetName}-下限`,
            type: 'line',
            data: [pump.lowerLine],
            smooth: false,
            symbol: 'none',
            lineStyle: {
                color: '#e6a23c',
                type: 'dashed',
                width: 2
            },
            showSymbol: false
        })
    })

    const option = {
        title: {
            text: selectedPump.value ? `${filteredData.value[0]?.assetName}监测点液位` : '泵房监测点液位',
            left: 'center',
            textStyle: {
                color: '#eef0f4'
            }
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            },
            formatter: function (params: any) {
                let result = '<div style="font-weight: bold;">监测点液位信息</div>'
                params.forEach((param: any) => {
                    if (param.value !== undefined) {
                        result += `<div style="margin: 5px 0;">
              <span style="color: ${param.color};">●</span>
              <span>${param.seriesName}: ${param.value}mm</span>
            </div>`
                    }
                })
                return result
            }
        },
        legend: {
            top: 30,
            type: 'scroll',
            textStyle: {
                color: '#eef0f4'
            }
        },
        grid: {
            left: 60,
            right: 60,
            bottom: 80,
            top: 80
        },
        xAxis: {
            type: 'category',
            data: ['液位高度'],
            axisLabel: {
                color: '#eef0f4'
            }
        },
        yAxis: {
            type: 'value',
            name: '液位(mm)',
            axisLabel: {
                color: '#eef0f4',
                formatter: '{value}'
            },
            splitLine: {
                lineStyle: {
                    color: '#2c3e50'
                }
            }
        },
        series: series
    }

    chartInstance.setOption(option)
}

// 刷新数据
const handleRefresh = () => {
    fetchData()
}

// 切换表格折叠状态
const toggleTableCollapse = () => {
    isTableCollapsed.value = !isTableCollapsed.value
}

// 启动自动刷新
const startAutoRefresh = () => {
    refreshTimer = setInterval(() => {
        fetchData()
    }, 30000) // 30秒刷新一次
}

// 组件挂载
onMounted(() => {
    fetchData()
    startAutoRefresh()

    // 监听窗口大小变化
    window.addEventListener('resize', () => {
        if (chartInstance) {
            chartInstance.resize()
        }
    })
})

// 组件卸载
onBeforeUnmount(() => {
    if (refreshTimer) {
        clearInterval(refreshTimer)
    }
    if (chartInstance) {
        chartInstance.dispose()
    }
})
</script>

<style scoped lang="scss">
.pump-data-container {
    padding: 20px;
    min-height: calc(100vh - 120px);
}

.header-section {
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .title-area {
        display: flex;
        align-items: center;
        margin-bottom: 16px;

        h2 {
            margin: 0 12px 0 0;
            color: #f1f3f5;
            font-size: 24px;
            font-weight: 600;
        }
    }

    .query-section {
        .demo-form-inline {
            margin: 0;
        }
    }
}

.stats-cards {
    margin-bottom: 10px;

    .stat-item {
        text-align: center;
        padding: 10px;

        .stat-value {
            font-size: 28px;
            font-weight: bold;
            color: #409eff;
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 14px;
            color: #eef0f4;
        }
    }
}

.content-section {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.chart-card,
.table-card {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .header-actions {
            display: flex;
            gap: 12px;
            align-items: center;
        }
    }
}

.chart-container {
    .empty-state {
        height: 400px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .chart {
        width: 100%;
    }
}

.level-value {
    font-weight: 600;

    &.level-normal {
        color: #67c23a;
    }

    &.level-warning {
        color: #e6a23c;
    }

    &.level-danger {
        color: #f56c6c;
    }
}

.device-status {
    display: flex;
    gap: 4px;

    .status-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;

        &.online {
            background-color: #67c23a;
        }

        &.offline {
            background-color: #f56c6c;
        }
    }
}

// 响应式适配
@media (max-width: 768px) {
    .pump-data-container {
        padding: 10px;
    }

    .stats-cards {
        .el-col {
            margin-bottom: 12px;
        }
    }

    .header-section {
        .title-area {
            flex-direction: column;
            align-items: flex-start;

            h2 {
                margin-bottom: 8px;
            }
        }
    }
}
</style>
