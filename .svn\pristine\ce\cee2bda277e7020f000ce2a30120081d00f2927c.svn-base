<template>
    <div class="event-card-wrapper">
        <div class="event-card">
            <div class="title-banner">
                <div class="banner-title">
                    <button class="nav-btn" @click="prevDefect" :disabled="defectList.length === 0" title="上一条缺陷">&nbsp;</button>
                    缺陷记录
                    <button class="nav-btn" @click="nextDefect" :disabled="defectList.length === 0" title="下一条缺陷">&nbsp;</button>
                </div>
                <div class="card-content" v-if="!loading && currentDefect">
                    <div class="info-row">
                        <span class="label">发现时间</span>
                        <span class="value">{{ formatTime(currentDefect.disoverTime) }}</span>
                    </div>

                    <div class="info-row">
                        <span class="label">缺陷位置</span>
                        <span class="value">{{ currentDefect.unitName || '未知位置' }}</span>
                    </div>

                    <div class="info-row">
                        <span class="label">缺陷描述</span>
                        <span class="value description">{{ currentDefect.description || '暂无描述' }}</span>
                    </div>
                </div>
                <div class="card-content loading-content" v-else-if="loading">
                    <div class="loading-text">加载中...</div>
                </div>
                <div class="card-content no-data-content" v-else>
                    <div class="no-data-text">暂无缺陷数据</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { listDiscoveredDefect } from '@/api/subProject/operation/defect/index'
import { DiscoveredDefectViewVO } from '@/api/subProject/operation/defect/types'

// 数据管理
const defectList = ref<DiscoveredDefectViewVO[]>([]) // 存储前100条缺陷
const currentIndex = ref(0) // 当前显示的缺陷索引
const currentDefect = ref<DiscoveredDefectViewVO | null>(null) // 当前显示的缺陷
const loading = ref(false) // 加载状态

// 定时器管理
let autoSwitchTimer: NodeJS.Timeout | null = null // 30秒自动切换
let dataRefreshTimer: NodeJS.Timeout | null = null // 30分钟数据刷新

// 获取前100条缺陷数据
const fetchTop100Defects = async () => {
    loading.value = true
    try {
        console.log('获取前100条缺陷数据...')
        const response = await listDiscoveredDefect({
            pageNum: 1,
            pageSize: 100 // 获取前100条
        })

        const defects = response.rows || []
        console.log('获取到缺陷数据:', defects.length, '条')

        // 按发现时间倒序排序（最新的在前）
        defects.sort((a, b) => {
            const timeA = new Date(a.disoverTime || '').getTime()
            const timeB = new Date(b.disoverTime || '').getTime()
            return timeB - timeA
        })

        defectList.value = defects.slice(0, 100) // 确保只取前100条
        currentIndex.value = 0
        updateCurrentDefect()
    } catch (error) {
        console.error('获取缺陷数据失败:', error)
        defectList.value = []
        currentDefect.value = null
    } finally {
        loading.value = false
    }
}

// 更新当前显示的缺陷
const updateCurrentDefect = () => {
    if (defectList.value.length === 0) {
        currentDefect.value = null
        return
    }

    currentDefect.value = defectList.value[currentIndex.value]
}

// 切换到上一条缺陷
const prevDefect = () => {
    if (defectList.value.length === 0) return
    currentIndex.value = currentIndex.value > 0 ? currentIndex.value - 1 : defectList.value.length - 1
    updateCurrentDefect()
    //console.log('切换到上一条缺陷，当前索引:', currentIndex.value)
}

// 切换到下一条缺陷
const nextDefect = () => {
    if (defectList.value.length === 0) return
    currentIndex.value = (currentIndex.value + 1) % defectList.value.length
    updateCurrentDefect()
    //console.log('切换到下一条缺陷，当前索引:', currentIndex.value)
}

// 启动自动切换定时器
const startAutoSwitch = () => {
    if (autoSwitchTimer) clearInterval(autoSwitchTimer)
    autoSwitchTimer = setInterval(() => {
        nextDefect()
    }, 30 * 1000) // 30秒
    console.log('已启动30秒自动切换定时器')
}

// 启动数据刷新定时器
const startDataRefresh = () => {
    if (dataRefreshTimer) clearInterval(dataRefreshTimer)
    dataRefreshTimer = setInterval(
        () => {
            console.log('定时刷新缺陷数据...')
            fetchTop100Defects()
        },
        30 * 60 * 1000
    ) // 30分钟
    console.log('已启动30分钟数据刷新定时器')
}

// 时间格式化
const formatTime = (time?: string) => {
    if (!time) return '暂无时间'

    try {
        const date = new Date(time)
        if (isNaN(date.getTime())) {
            return time
        }

        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        const hours = String(date.getHours()).padStart(2, '0')
        const minutes = String(date.getMinutes()).padStart(2, '0')
        const seconds = String(date.getSeconds()).padStart(2, '0')

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    } catch (error) {
        return time
    }
}

onMounted(async () => {
    await fetchTop100Defects()
    startAutoSwitch()
    startDataRefresh()
})

onUnmounted(() => {
    if (autoSwitchTimer) {
        clearInterval(autoSwitchTimer)
        autoSwitchTimer = null
    }
    if (dataRefreshTimer) {
        clearInterval(dataRefreshTimer)
        dataRefreshTimer = null
    }
    console.log('已清理所有定时器')
})
</script>

<style lang="scss" scoped>
.event-card-wrapper {
    width: 70%;
    height: 90%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;

    // 左侧装饰图片
    &::before {
        content: '';
        position: absolute;
        left: -35px;
        top: 140px;
        transform: translateY(-50%);
        width: 60px;
        height: 210px;
        background-image: url('@/assets/images/left.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-position: center;
        z-index: 1;
        // background: red;
    }

    // 右侧装饰图片
    &::after {
        content: '';
        position: absolute;
        right: -35px;
        top: 140px;
        transform: translateY(-50%);
        width: 60px;
        height: 210px;
        background-image: url('@/assets/images/right.png');
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
        z-index: 1;
    }
}

.event-card {
    width: 100%;
    height: 100%;
    // background: rgba(0,44,94,0.67);
    // box-shadow: inset 0px -7px 23px 0px rgba(168,209,255,0.13);
    // border-radius: 8px;
    // border: 1px solid rgba(52, 152, 255, 0.3);
    padding: 0;
    color: #fff;
    font-family: 'Microsoft YaHei', sans-serif;
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 2;
}

.title-banner {
    width: 100%;
    height: 100%;
    background-image: url('@/assets/images/Frame.png');
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    flex-shrink: 0;
    // background: red;
}

.banner-title {
    font-size: 20px;
    font-weight: bold;
    color: #fff;
    text-align: center;
    letter-spacing: 1px;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    padding: 10px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    gap: 15px;

    .nav-btn {
        background: rgba(255, 255, 255, 0);
        border: 0px solid rgba(255, 255, 255, 0.3);
        color: #fff;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        cursor: pointer;
        font-size: 18px;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;

        // &:hover:not(:disabled) {
        //     background: rgba(255, 255, 255, 0.2);
        //     border-color: rgba(255, 255, 255, 0.5);
        //     transform: scale(1.1);
        // }

        &:disabled {
            opacity: 0.3;
            cursor: not-allowed;
        }
    }
}



.card-content {
    display: flex;
    flex-direction: column;
    gap: 10px;
    flex: 1;
    padding: 0 16px 16px 16px;
    width: 100%;
    max-height: 200px;
    overflow-y: auto;
    overflow-x: hidden;
    &::-webkit-scrollbar {
        width: 8px;
    }
    &::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
        background: rgba(52, 152, 255, 0.6);
        border-radius: 4px;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
    &::-webkit-scrollbar-thumb:hover {
        background: rgba(52, 152, 255, 0.8);
    }
}

.info-row {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    padding: 6px 0;
}

.info-row:last-child {
    border-bottom: none;
}

.label {
    font-weight: 500;
    min-width: 64px;
    flex-shrink: 0;
    margin-right: 10px;
}

.value {
    color: #fff;
    font-size: 14px;
    line-height: 1.4;
    flex: 1;
}

.value.description {
    line-height: 1.5;
    text-align: justify;
}

// 加载状态样式
.loading-content {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 120px;

    .loading-text {
        color: rgba(255, 255, 255, 0.8);
        font-size: 16px;
    }
}

// 无数据状态样式
.no-data-content {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 120px;

    .no-data-text {
        color: rgba(255, 255, 255, 0.6);
        font-size: 16px;
    }
}
</style>
