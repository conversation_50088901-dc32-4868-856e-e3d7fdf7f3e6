<template>
    <el-card class="box-card" v-if="delayHistory.length > 0">
        <div class="task-delay-history">
            <div class="header" style="color: white">
                <h3>任务延期历史记录</h3>
            </div>

            <el-table :data="delayHistory" v-loading="loading" empty-text="暂无延期记录" stripe border>
                <el-table-column prop="nickName" label="延期执行人" width="120" align="center" />
                <el-table-column prop="originalDate" label="原计划时间" width="180" align="center">
                    <template #default="scope">
                        {{ formatDate(scope.row.originalDate) }}
                    </template>
                </el-table-column>
                <el-table-column prop="bgnDate" label="延期后时间" width="180" align="center">
                    <template #default="{ row }">
                        {{ formatDate(row.bgnDate) }}
                    </template>
                </el-table-column>
                <el-table-column prop="remark" label="延期原因" min-width="200" show-overflow-tooltip />
                <el-table-column prop="createTime" label="延期时间" width="180" align="center">
                    <template #default="{ row }">
                        {{ formatDateTime(row.createTime) }}
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </el-card>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getDelayHistory } from '@/api/plan/delayTask'
import { DelayTaskVO } from '@/api/plan/delayTask/types'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'

interface Props {
    taskId: string | number
}

const props = defineProps<Props>()

const loading = ref(false)
const delayHistory = ref<DelayTaskVO[]>([])

// 获取延期历史记录
const loadDelayHistory = async () => {
    try {
        loading.value = true
        const response = await getDelayHistory(props.taskId)
        delayHistory.value = response.data || []
    } catch (error) {
        console.error('获取延期历史记录失败:', error)
        ElMessage.error('获取延期历史记录失败')
    } finally {
        loading.value = false
    }
}

// 格式化日期
const formatDate = (date: string | Date) => {
    if (!date) return '-'
    return dayjs(date).format('YYYY-MM-DD')
}

// 格式化日期时间
const formatDateTime = (date: string | Date) => {
    if (!date) return '-'
    return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}

onMounted(() => {
    if (props.taskId) {
        loadDelayHistory()
    }
})

// 暴露刷新方法
defineExpose({
    refresh: loadDelayHistory
})
</script>

<style scoped>
.task-delay-history {
    padding: 16px;
}

.header {
    margin-bottom: 16px;
}

.header h3 {
    margin: 0;
    color: #303133;
    font-size: 16px;
    font-weight: 500;
}
</style>
