export interface InspectionResultWithTaskViewVO {
    /**
     * 主键ID
     */
    id: string | number

    /**
     * 设备ID
     */
    deviceId: string | number

    /**
     * 线路ID
     */
    lineId: string | number

    /**
     * 线路名称/路线名称
     */
    lineName: string

    /**
     * 巡检项ID
     */
    itemId: string | number

    /**
     * 字符串值
     */
    valueString: string

    /**
     * 布尔值
     */
    valueBool: string

    /**
     * 数据值
     */
    valueData: string

    /**
     * 任务名称
     */
    taskName: string

    /**
     * 开始日期
     */
    bgnDate: string

    /**
     * 任务开始日期
     */
    taskStartDate: string

    /**
     * 任务完成日期
     */
    taskFinishDate: string

    /**
     * 班次类型
     */
    shiftType: string
}

export interface InspectionResultWithTaskViewForm extends BaseEntity {
    /**
     * 主键ID
     */
    id?: string | number

    /**
     * 设备ID
     */
    deviceId?: string | number

    /**
     * 线路ID
     */
    lineId?: string | number

    /**
     * 线路名称/路线名称
     */
    lineName?: string

    /**
     * 巡检项ID
     */
    itemId?: string | number

    /**
     * 字符串值
     */
    valueString?: string

    /**
     * 布尔值
     */
    valueBool?: string

    /**
     * 数据值
     */
    valueData?: string

    /**
     * 任务名称
     */
    taskName?: string

    /**
     * 开始日期
     */
    bgnDate?: string

    /**
     * 任务开始日期
     */
    taskStartDate?: string

    /**
     * 任务完成日期
     */
    taskFinishDate?: string

    /**
     * 班次类型
     */
    shiftType?: string
}

export interface InspectionResultWithTaskViewQuery extends PageQuery {
    /**
     * 设备ID
     */
    deviceId?: string | number

    /**
     * 线路ID
     */
    lineId?: string | number

    /**
     * 线路名称/路线名称
     */
    lineName?: string

    /**
     * 巡检项ID
     */
    itemId?: string | number

    /**
     * 任务名称
     */
    taskName?: string

    /**
     * 班次类型
     */
    shiftType?: string

    /**
     * 开始日期范围
     */
    bgnDateRange?: string[]

    /**
     * 任务开始日期范围
     */
    taskStartDateRange?: string[]

    /**
     * 任务完成日期范围
     */
    taskFinishDateRange?: string[]

    /**
     * 日期范围参数
     */
    params?: any
}
