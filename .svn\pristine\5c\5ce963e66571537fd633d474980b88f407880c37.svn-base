<!--
巡检记录表格组件 (InspectionRecordTable)

功能：
- 显示巡检记录相关信息
- 自动处理数据字典显示
- 内置分页功能
- 支持详情查看
- 内置查询条件筛选（仅作业单类型）
- 支持按设备ID查询巡检记录

使用示例：

1. 基本使用（不包含设备ID查询）：
<InspectionRecordTable
    :table-data="inspectionData"
    :loading="loading"
    :total="total"
    :query-params="queryParams"
    :show-filter="true"
    @view-details="handleViewDetails"
    @pagination="handlePagination"
    @query="handleQuery"
    @reset="handleReset"
/>

2. 设备巡检记录查询使用：
<InspectionRecordTable
    v-model:table-data="inspectionData"
    v-model:total="total"
    :loading="loading"
    :query-params="queryParams"
    :show-filter="true"
    :device-id="deviceId"
    :project-id="projectId"
    @view-details="handleViewDetails"
    @pagination="handlePagination"
    @query="handleQuery"
    @reset="handleReset"
/>

Props:
- tableData: 表格数据数组
- loading: 加载状态
- total: 总记录数
- queryParams: 分页参数 { pageNum, pageSize }
- showFilter: 是否显示查询条件 (默认true)
- deviceId: 设备ID (可选，用于查询该设备的巡检记录)
- projectId: 项目ID (可选，用于设备巡检记录查询)

Events:
- view-details: 查看详情事件
- pagination: 分页事件
- query: 查询事件
- reset: 重置事件
- update:tableData: 表格数据更新事件（当使用设备ID查询时）
- update:total: 总数更新事件（当使用设备ID查询时）
- view-report: 查看报告事件

特性说明：
1. 当提供deviceId和projectId时，组件会自动调用相关API
2. 支持按作业单类型条件过滤
3. 当设备ID变化时，组件会自动重新查询
4. 支持巡检记录状态显示
5. 提供查看详情的操作按钮
-->

<template>
    <el-card shadow="never" class="table-card">
        <!-- 查询条件区域 -->
        <div class="filter-section">
            <div class="filter-row">
                <div class="filter-item">
                    <el-select v-model="filterParams.taskType" placeholder="请选择作业单类型" clearable>
                        <el-option v-for="item in taskTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </div>

                <!-- 查询按钮 -->
                <div class="filter-actions">
                    <el-button type="primary" @click="handleQuery">查询</el-button>
                    <el-button @click="handleReset">重置</el-button>
                </div>
            </div>
        </div>

        <el-table :data="tableData" stripe style="width: 100%" v-loading="loading" :empty-text="loading ? '加载中...' : '暂无数据'">
            <!-- 作业单名称 -->
            <el-table-column prop="name" label="作业单名称" min-width="200" />

            <!-- 计划作业日期 -->
            <el-table-column prop="taskDate" label="计划作业日期" width="180">
                <template #default="scope">
                    {{ formatDate(scope.row.taskDate) }}
                </template>
            </el-table-column>

            <!-- 班次 -->
            <el-table-column prop="shiftType" label="班次" width="100">
                <template #default="scope">
                    <dict-tag :options="shiftTypeOptions" :value="scope.row.shiftType" />
                </template>
            </el-table-column>

            <!-- 巡检班组 -->
            <!-- <el-table-column prop="inspectionTeamMember" label="巡检班组" width="120">
                <template #default="scope">
                    {{ scope.row.inspectionTeamMember || '-' }}
                </template>
            </el-table-column> -->

            <!-- 巡检路线 -->
            <!-- <el-table-column prop="inspectionLineId" label="巡检路线" width="120">
                <template #default="scope">
                    {{ scope.row.inspectionLineId || '-' }}
                </template>
            </el-table-column> -->

            <!-- 实际开工日期 -->
            <el-table-column prop="bgnDate" label="实际开工日期" width="180">
                <template #default="scope">
                    {{ formatDate(scope.row.bgnDate) }}
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页组件 -->
        <pagination v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="handlePagination" />
    </el-card>
</template>

<script setup lang="ts">
import { ref, computed, getCurrentInstance, toRefs, watch } from 'vue'
import type { ComponentInternalInstance } from 'vue'
import { useAppStore } from '@/store/modules/app'
import { queryDeviceRelatedTaskList } from '@/api/statistics/dossier'
import { type QueryDeviceCuringTaskDossierQuery } from '@/api/statistics/dossier/types'
import { TaskVO } from '@/api/plan/task/types'

// 定义组件属性
interface Props {
    deviceId?: string
    dateRange?: {
        startDate: string
        endDate: string
    }
}

// 查询条件参数接口
interface FilterParams {
    taskType: string
    deviceId?: string
    projectId?: string
    startDate?: string
    endDate?: string
}

const props = withDefaults(defineProps<Props>(), {
    deviceId: '',
    dateRange: () => ({ startDate: '', endDate: '' })
})

const appStore = useAppStore()

const { proxy } = getCurrentInstance() as ComponentInternalInstance

// 内部数据状态
const tableData = ref<TaskVO[]>([])
const loading = ref(false)
const total = ref(0)
const queryParams = ref({
    pageNum: 1,
    pageSize: 10
})

// 查询条件
const filterParams = ref<FilterParams>({
    taskType: '',
    deviceId: props.deviceId,
    projectId: appStore.projectContext.selectedProjectId
})

// 先定义handleQuery
const handleQuery = async () => {
    if (!filterParams.value.deviceId || !filterParams.value.projectId) {
        return
    }

    try {
        loading.value = true

        // 构建查询参数 - 使用设备任务查询接口，通过taskType区分巡检任务
        const apiQueryParams: QueryDeviceCuringTaskDossierQuery = {
            deviceId: filterParams.value.deviceId || props.deviceId, // 设备ID
            projectId: filterParams.value.projectId, // 项目ID
            taskType: 'inspect', // 任务类型：巡检
            pageNum: queryParams.value.pageNum,
            pageSize: queryParams.value.pageSize
        }

        // 根据作业单类型过滤
        if (filterParams.value.taskType) {
            apiQueryParams.taskTypeSub = filterParams.value.taskType
        }

        // 处理日期范围 - 按bgnDate和endDate查询（任务时间范围交集）
        if (props.dateRange && props.dateRange.startDate && props.dateRange.endDate) {
            apiQueryParams.params = {
                ...apiQueryParams.params,
                beginBgnDate: props.dateRange.startDate,
                endBgnDate: props.dateRange.endDate
            }
        }

        console.log('巡检记录查询参数:', apiQueryParams)

        // 调用设备相关任务查询API（通过taskType=inspect区分巡检任务）
        const response = await queryDeviceRelatedTaskList(apiQueryParams)

        // 处理新的设备巡检记录API响应数据
        console.log('巡检记录API响应:', response)

        if (response && response.rows && Array.isArray(response.rows)) {
            // 新的API返回格式：{ data: { rows: [], total: number } }
            tableData.value = response.rows
            total.value = response.total || 0
        } else if (response && Array.isArray(response)) {
            // 直接返回数组格式
            tableData.value = response
            total.value = response.length
        } else {
            console.warn('巡检记录API响应格式异常:', response)
            tableData.value = []
            total.value = 0
        }
    } catch (error) {
        console.error('查询设备巡检记录失败:', error)
        tableData.value = []
        total.value = 0
    } finally {
        loading.value = false
    }
}

// 监听设备ID变化
watch(
    () => props.deviceId,
    (newDeviceId) => {
        filterParams.value.deviceId = newDeviceId
        if (newDeviceId) {
            // 当设备ID变化时，自动查询该设备的巡检记录
            handleQuery()
        }
    },
    { immediate: true }
)

// 监听项目ID变化
watch(
    () => appStore.projectContext.selectedProjectId,
    (newProjectId) => {
        filterParams.value.projectId = newProjectId
    }
)

// 监听时间范围变化
watch(
    () => props.dateRange,
    (newDateRange) => {
        if (newDateRange && newDateRange.startDate && newDateRange.endDate) {
            // 当时间范围变化时，自动触发查询
            handleQuery()
        }
    },
    { deep: true }
)

// 获取数据字典
const { task_type, work_order_white_evening, task_status } = toRefs<any>(proxy?.useDict('task_type', 'work_order_white_evening', 'task_status'))

// 数据字典选项
const taskTypeOptions = computed(() => {
    return task_type.value || []
})

const shiftTypeOptions = computed(() => {
    return work_order_white_evening.value || []
})

const taskStatusOptions = computed(() => {
    return task_status.value || []
})

// 格式化日期
const formatDate = (date: string) => {
    if (!date) return '-'
    return date
}

// 获取巡检结果类型
const getInspectionResultType = (result: string) => {
    switch (result) {
        case 'completed':
            return 'success'
        case 'in_progress':
            return 'warning'
        case 'pending':
            return 'info'
        case 'exception':
            return 'danger'
        default:
            return 'info'
    }
}

// 获取巡检结果文本
const getInspectionResultText = (result: string) => {
    switch (result) {
        case 'completed':
            return '已完成'
        case 'in_progress':
            return '进行中'
        case 'pending':
            return '待开始'
        case 'exception':
            return '异常'
        default:
            return '未知'
    }
}

// 查看详情
const handleViewDetails = (row: TaskVO) => {
    // 处理查看详情逻辑
}

// 查看报告
const handleViewReport = (row: TaskVO) => {
    // 处理查看报告逻辑
}

// 分页处理
const handlePagination = (params: { pageNum: number; pageSize: number }) => {
    // 触发查询，获取新页面的数据
    handleQuery()
}

// 重置处理
const handleReset = () => {
    filterParams.value = {
        taskType: '',
        deviceId: props.deviceId,
        projectId: appStore.projectContext.selectedProjectId
    }
    // 重置时触发查询
    handleQuery()
}
</script>

<style lang="scss" scoped>
.table-card {
    margin-top: 16px;
}

.filter-section {
    margin-bottom: 16px;
    padding: 16px;
    border-radius: 4px;
}

.filter-row {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
}

.filter-item {
    min-width: 200px;
}

.filter-actions {
    display: flex;
    gap: 8px;
    margin-left: auto;
}

@media (max-width: 768px) {
    .filter-row {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-item {
        min-width: auto;
    }

    .filter-actions {
        margin-left: 0;
        margin-top: 16px;
    }
}
</style>
