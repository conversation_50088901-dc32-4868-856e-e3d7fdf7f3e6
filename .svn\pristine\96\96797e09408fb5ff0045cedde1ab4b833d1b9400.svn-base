<template>
    <div class="acc-water-page">
        <div class="panel-form">
            <div class="panel-title">新增积水点</div>
            <el-form :model="form" label-width="48px" class="form-inline">
                <div class="row row-3">
                    <el-form-item label="经度">
                        <el-input v-model="form.lng" placeholder="请输入经度" />
                    </el-form-item>
                    <el-form-item label="纬度">
                        <el-input v-model="form.lat" placeholder="请输入纬度" />
                    </el-form-item>
                    <el-form-item label="名称">
                        <el-input v-model="form.name" placeholder="请输入积水点名称" />
                    </el-form-item>
                </div>
                <div class="row">
                    <el-form-item label="内容" class="item-content">
                        <el-input v-model="form.content" type="textarea" :rows="6" placeholder="请输入积水点详细内容" />
                    </el-form-item>
                </div>
                <div class="actions">
                    <el-button type="primary" class="confirm-btn" @click="handleAdd">确认新增</el-button>
                </div>
            </el-form>
        </div>

        <div class="panel panel-table">
            <el-table :data="pagedData" style="width: 100%" :header-cell-style="tableHeaderStyle" :cell-style="tableCellStyle">
                <el-table-column type="index" label="序号" width="80" :index="indexMethod" />
                <el-table-column prop="name" label="积水点名称" min-width="220" />
                <el-table-column prop="lng" label="经度" min-width="120" />
                <el-table-column prop="lat" label="纬度" min-width="120" />
                <el-table-column prop="content" label="内容" min-width="280" show-overflow-tooltip />
                <el-table-column label="操作" width="160">
                    <template #default="{ row }">
                        <el-button type="primary" link style="color: #42F3E9;">
                            <el-icon size="16"><EditPen /></el-icon>
                            修改
                        </el-button>
                        <el-button type="danger" link>
                            <el-icon size="16"><Delete /></el-icon>
                            删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>

            <div class="pagination-wrapper">
                <pagination
                    v-show="total > 0"
                    :total="total"
                    v-model:page="queryParams.pageNum"
                    v-model:limit="queryParams.pageSize"

                />
            </div>
        </div>
    </div>

</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { EditPen, Delete } from '@element-plus/icons-vue'

interface WaterPointItem {
    id: number
    name: string
    lng: string
    lat: string
    content: string
}

const form = ref({
    lng: '',
    lat: '',
    name: '',
    content: ''
})

const queryParams = ref({
    pageNum: 1,
    pageSize: 9
})

const seedRows: Omit<WaterPointItem, 'id'>[] = [
    { name: '行车蓄能0001', lng: '35度', lat: '187度', content: '主体结构' },
    { name: '埃努纵向缓坡', lng: '45度', lat: '54度', content: '供配电和照明系统' },
    { name: '混凝土路肩', lng: '76度', lat: '76度', content: '供配电和照明系统' },
    { name: '框梁式市政基础', lng: '54度', lat: '76度', content: '供配电和照明系统' },
    { name: '露滴铭钻', lng: '87度', lat: '88度', content: '供配电和照明系统' },
    { name: '框梁式市政基础', lng: '65度', lat: '126度', content: '供配电和照明系统' },
    { name: '露滴铭钻', lng: '33度', lat: '176度', content: '供配电和照明系统' },
    { name: '埃努纵向缓坡', lng: '18度', lat: '26度', content: '供配电和照明系统' },
    { name: '混凝土路肩', lng: '65度', lat: '29度', content: '供配电和照明系统' }
]

const tableData = ref<WaterPointItem[]>(Array.from({ length: 121 }).map((_, i) => ({
    id: i + 1,
    ...seedRows[i % seedRows.length]
})))

const total = computed(() => tableData.value.length)

const pagedData = computed(() => {
    const start = (queryParams.value.pageNum - 1) * queryParams.value.pageSize
    return tableData.value.slice(start, start + queryParams.value.pageSize)
})

function indexMethod(index: number) {
    return (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1
}

function handleAdd() {
    if (!form.value.name || !form.value.lng || !form.value.lat) {
        ElMessage.warning('请完整填写经度、纬度和名称')
        return
    }

}

function tableHeaderStyle() {
    return {
        background: 'linear-gradient(180deg, #1D2F58 0%)',
        color: '#cfe3ff',
        fontWeight: 500
    }
}

function tableCellStyle() {
    return {
        background: 'linear-gradient(0deg, rgba(8,24,61,0.8) 0%)',
        color: '#e7f1ff'
    }
}
</script>

<style scoped lang="scss">
.acc-water-page {
    padding: 10px;
    color: #e7f1ff;

    min-height: calc(100vh - 120px);
    // background: red;
}


.panel-form {
    padding: 15px 30px;
    margin-bottom: 18px;
    width: 60%;
    margin-left: auto;
    margin-right: auto;
}

.panel-title {
    text-align: center;
    font-size: 22px;
    letter-spacing: 2px;
    color: #fff;
    border-bottom: none;
}


.row {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;
}

.row-3 {
    grid-template-columns: repeat(3, 1fr);
}


.actions {
    display: flex;
    justify-content: center;
    padding: 10px 0 6px;
}

.confirm-btn {
    padding: 5px 80px;
    // background: red;
    background: #4286F3;
}

.panel {
    border: none;
    background: transparent;
    box-shadow: none;
}

.panel-table {
    padding:10px;
// background: red;
}



.pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    // background: red;
}


.panel-form :deep(.el-input__wrapper) {
    background-color: #2A3954;
    box-shadow: none;
    color: #e7f1ff;
    border: none;
}

.panel-form :deep(.el-textarea__inner) {
    background-color: #0E1F44;
    box-shadow: none;
    color: #e7f1ff;
    border: none;
}

</style>
