export interface LifeCycleVO {
    /**
     * 主键ID
     */
    id: string | number

    /**
     * 项目ID
     */
    projectId: string

    /**
     * 分类，默认：default，扩展用
     */
    category?: string

    /**
     * 专业类别代码，对应字典项tnl_specialty
     */
    specialtyCode: string

    /**
     * 分值
     */
    score: number

    /**
     * 年份
     */
    year: number

    /**
     * 月份
     */
    month: number

    /**
     * 详细评分数据(JSON格式)
     */
    detailScores?: string

    /**
     * 创建时间
     */
    createTime?: string

    /**
     * 更新时间
     */
    updateTime?: string
}

export interface LifeCycleForm {
    /**
     * 主键ID
     */
    id?: string | number

    /**
     * 项目ID
     */
    projectId?: string

    /**
     * 分类，默认：default，扩展用
     */
    category?: string

    /**
     * 专业类别代码，对应字典项tnl_specialty
     */
    specialtyCode?: string

    /**
     * 分值
     */
    score?: number

    /**
     * 年份
     */
    year?: number

    /**
     * 月份
     */
    month?: number

    /**
     * 详细评分数据(JSON格式字符串)
     */
    detailScores?: string

    /**
     * 日期选择器用字段
     */
    selectedDate?: string
}

export interface LifeCycleQuery {
    /**
     * 分页参数
     */
    pageNum?: number
    pageSize?: number
    /**
     * 项目ID
     */
    projectId?: string

    /**
     * 分类，默认：default，扩展用
     */
    category?: string

    /**
     * 专业类别代码，对应字典项tnl_specialty
     */
    specialtyCode?: string

    /**
     * 年份
     */
    year?: number

    /**
     * 月份
     */
    month?: number

    /**
     * 开始年份
     */
    startYear?: number

    /**
     * 结束年份
     */
    endYear?: number

    /**
     * 开始月份
     */
    startMonth?: number

    /**
     * 结束月份
     */
    endMonth?: number

    /**
     * 年月范围参数
     */
    startYearMonth?: string  // 格式：2024-05
    endYearMonth?: string    // 格式：2025-07

    /**
     * 日期范围参数
     */
    params?: any
}

export interface ChartDataResponse {
    /**
     * 日期数组
     */
    dates: string[]

    /**
     * 分值数组
     */
    scores: number[]
}

export interface DataItem {
    /**
     * 数据项ID
     */
    id: string

    /**
     * 数据项名称
     */
    name: string
}
