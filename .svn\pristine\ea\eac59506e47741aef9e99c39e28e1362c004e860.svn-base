export interface RoomVO {
    /**
     *
     */
    id: string | number

    /**
     *
     */
    unitId: string | number

    /**
     *
     */
    projectId: string | number

    /**
     * 房间号/房间名称
     */
    roomNane: string

    /**
     * 房间编码
     */
    roomCode: string

    /**
     * 楼层数
     */
    floor: string
}

export interface RoomForm extends BaseEntity {
    /**
     *
     */
    id?: string | number

    /**
     *
     */
    unitId?: string | number

    /**
     *
     */
    projectId?: string | number

    /**
     * 房间号/房间名称
     */
    roomNane?: string

    /**
     * 房间编码
     */
    roomCode?: string

    /**
     * 楼层数
     */
    floor?: string
}

export interface RoomQuery extends PageQuery {
    /**
     *
     */
    unitId?: string | number

    /**
     *
     */
    projectId?: string | number

    /**
     * 房间号/房间名称
     */
    roomNane?: string

    /**
     * 房间编码
     */
    roomCode?: string

    /**
     * 日期范围参数
     */
    params?: any
}
