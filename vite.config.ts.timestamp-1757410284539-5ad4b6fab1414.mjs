// vite.config.ts
import { defineConfig, loadEnv } from "file:///D:/wtgit/%E7%9B%B8%E5%9F%8E%E9%9A%A7%E9%81%93/df-vue-plus-ui/node_modules/vite/dist/node/index.js";

// vite/plugins/index.ts
import vue from "file:///D:/wtgit/%E7%9B%B8%E5%9F%8E%E9%9A%A7%E9%81%93/df-vue-plus-ui/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import vueDevTools from "file:///D:/wtgit/%E7%9B%B8%E5%9F%8E%E9%9A%A7%E9%81%93/df-vue-plus-ui/node_modules/vite-plugin-vue-devtools/dist/vite.mjs";

// vite/plugins/unocss.ts
import UnoCss from "file:///D:/wtgit/%E7%9B%B8%E5%9F%8E%E9%9A%A7%E9%81%93/df-vue-plus-ui/node_modules/unocss/dist/vite.mjs";
var unocss_default = () => {
  return UnoCss({
    hmrTopLevelAwait: false
    // unocss默认是true，低版本浏览器是不支持的，启动后会报错
  });
};

// vite/plugins/auto-import.ts
import AutoImport from "file:///D:/wtgit/%E7%9B%B8%E5%9F%8E%E9%9A%A7%E9%81%93/df-vue-plus-ui/node_modules/unplugin-auto-import/dist/vite.js";
import { ElementPlusResolver } from "file:///D:/wtgit/%E7%9B%B8%E5%9F%8E%E9%9A%A7%E9%81%93/df-vue-plus-ui/node_modules/unplugin-vue-components/dist/resolvers.js";
var __vite_injected_original_dirname = "D:\\wtgit\\\u76F8\u57CE\u96A7\u9053\\df-vue-plus-ui\\vite\\plugins";
var auto_import_default = (path3) => {
  return AutoImport({
    // 自动导入 Vue 相关函数
    imports: ["vue", "vue-router", "@vueuse/core", "pinia"],
    eslintrc: {
      enabled: true,
      filepath: "./.eslintrc-auto-import.json",
      globalsPropValue: true
    },
    resolvers: [
      // 自动导入 Element Plus 相关函数ElMessage, ElMessageBox... (带样式)
      ElementPlusResolver()
    ],
    vueTemplate: true,
    // 是否在 vue 模板中自动导入
    dts: path3.resolve(path3.resolve(__vite_injected_original_dirname, "../../src"), "types", "auto-imports.d.ts")
  });
};

// vite/plugins/components.ts
import Components from "file:///D:/wtgit/%E7%9B%B8%E5%9F%8E%E9%9A%A7%E9%81%93/df-vue-plus-ui/node_modules/unplugin-vue-components/dist/vite.js";
import { ElementPlusResolver as ElementPlusResolver2 } from "file:///D:/wtgit/%E7%9B%B8%E5%9F%8E%E9%9A%A7%E9%81%93/df-vue-plus-ui/node_modules/unplugin-vue-components/dist/resolvers.js";
import IconsResolver from "file:///D:/wtgit/%E7%9B%B8%E5%9F%8E%E9%9A%A7%E9%81%93/df-vue-plus-ui/node_modules/unplugin-icons/dist/resolver.js";
var __vite_injected_original_dirname2 = "D:\\wtgit\\\u76F8\u57CE\u96A7\u9053\\df-vue-plus-ui\\vite\\plugins";
var components_default = (path3) => {
  return Components({
    resolvers: [
      // 自动导入 Element Plus 组件
      ElementPlusResolver2(),
      // 自动注册图标组件
      IconsResolver({
        enabledCollections: ["ep"]
      })
    ],
    dts: path3.resolve(path3.resolve(__vite_injected_original_dirname2, "../../src"), "types", "components.d.ts")
  });
};

// vite/plugins/icons.ts
import Icons from "file:///D:/wtgit/%E7%9B%B8%E5%9F%8E%E9%9A%A7%E9%81%93/df-vue-plus-ui/node_modules/unplugin-icons/dist/vite.js";
var icons_default = () => {
  return Icons({
    // 自动安装图标库
    autoInstall: true
  });
};

// vite/plugins/svg-icon.ts
import { createSvgIconsPlugin } from "file:///D:/wtgit/%E7%9B%B8%E5%9F%8E%E9%9A%A7%E9%81%93/df-vue-plus-ui/node_modules/vite-plugin-svg-icons-ng/dist/index.mjs";
var __vite_injected_original_dirname3 = "D:\\wtgit\\\u76F8\u57CE\u96A7\u9053\\df-vue-plus-ui\\vite\\plugins";
var svg_icon_default = (path3) => {
  return createSvgIconsPlugin({
    // 指定需要缓存的图标文件夹
    iconDirs: [path3.resolve(path3.resolve(__vite_injected_original_dirname3, "../../src"), "assets/icons/svg")],
    // 指定symbolId格式
    symbolId: "icon-[dir]-[name]"
  });
};

// vite/plugins/compression.ts
import compression from "file:///D:/wtgit/%E7%9B%B8%E5%9F%8E%E9%9A%A7%E9%81%93/df-vue-plus-ui/node_modules/vite-plugin-compression/dist/index.mjs";
var compression_default = (env) => {
  const { VITE_BUILD_COMPRESS } = env;
  const plugin = [];
  if (VITE_BUILD_COMPRESS) {
    const compressList = VITE_BUILD_COMPRESS.split(",");
    if (compressList.includes("gzip")) {
      plugin.push(
        compression({
          ext: ".gz",
          deleteOriginFile: false
        })
      );
    }
    if (compressList.includes("brotli")) {
      plugin.push(
        compression({
          ext: ".br",
          algorithm: "brotliCompress",
          deleteOriginFile: false
        })
      );
    }
  }
  return plugin;
};

// vite/plugins/setup-extend.ts
import setupExtend from "file:///D:/wtgit/%E7%9B%B8%E5%9F%8E%E9%9A%A7%E9%81%93/df-vue-plus-ui/node_modules/unplugin-vue-setup-extend-plus/dist/vite.js";
var setup_extend_default = () => {
  return setupExtend({});
};

// vite/plugins/index.ts
import path from "path";
var plugins_default = (viteEnv, isBuild = false) => {
  const vitePlugins = [];
  vitePlugins.push(vue());
  vitePlugins.push(vueDevTools());
  vitePlugins.push(unocss_default());
  vitePlugins.push(auto_import_default(path));
  vitePlugins.push(components_default(path));
  vitePlugins.push(compression_default(viteEnv));
  vitePlugins.push(icons_default());
  vitePlugins.push(svg_icon_default(path));
  vitePlugins.push(setup_extend_default());
  return vitePlugins;
};

// vite.config.ts
import autoprefixer from "file:///D:/wtgit/%E7%9B%B8%E5%9F%8E%E9%9A%A7%E9%81%93/df-vue-plus-ui/node_modules/autoprefixer/lib/autoprefixer.js";
import path2 from "path";
var __vite_injected_original_dirname4 = "D:\\wtgit\\\u76F8\u57CE\u96A7\u9053\\df-vue-plus-ui";
var vite_config_default = defineConfig(({ mode, command }) => {
  const env = loadEnv(mode, process.cwd());
  return {
    // 部署生产环境和开发环境下的URL。
    // 默认情况下，vite 会假设你的应用是被部署在一个域名的根路径上
    // 例如 https://www.ruoyi.vip/。如果应用被部署在一个子路径上，你就需要用这个选项指定这个子路径。例如，如果你的应用被部署在 https://www.ruoyi.vip/admin/，则设置 baseUrl 为 /admin/。
    base: env.VITE_APP_CONTEXT_PATH,
    resolve: {
      alias: {
        "@": path2.resolve(__vite_injected_original_dirname4, "./src")
      },
      extensions: [".mjs", ".js", ".ts", ".jsx", ".tsx", ".json", ".vue"]
    },
    // https://cn.vitejs.dev/config/#resolve-extensions
    plugins: plugins_default(env, command === "build"),
    server: {
      host: "0.0.0.0",
      port: Number(env.VITE_APP_PORT),
      open: true,
      proxy: {
        [env.VITE_APP_BASE_API]: {
          // target: 'http://localhost:8080',
          target: "https://zhgk.suzhousyt.com:48081/prod-api",
          // 隧道后端服务地址
          changeOrigin: true,
          ws: true,
          rewrite: (path3) => path3.replace(new RegExp("^" + env.VITE_APP_BASE_API), "")
        },
        // 默认开发环境，如果要调试生产环境，就改成/dev-api
        "/dev-api2": {
          target: "https://zhgk.suzhousyt.com:48081/prod-api",
          // 隧道后端服务地址
          changeOrigin: true,
          secure: true,
          // 使用HTTPS
          ws: true,
          rewrite: (path3) => path3.replace(/^\/dev-api/, ""),
          configure: (proxy, options) => {
            proxy.on("error", (err, req, res) => {
              console.log("\u96A7\u9053API\u4EE3\u7406\u9519\u8BEF:", err);
            });
            proxy.on("proxyReq", (proxyReq, req, res) => {
              console.log("\u96A7\u9053API\u4EE3\u7406\u8BF7\u6C42:", req.method, req.url);
            });
          }
        },
        // 和风天气API代理 - 使用专用API主机
        "/qweather-api": {
          target: "https://mm63yw27tf.yun.qweatherapi.com",
          changeOrigin: true,
          secure: true,
          // 使用HTTPS
          rewrite: (path3) => path3.replace(/^\/qweather-api/, ""),
          configure: (proxy) => {
            proxy.on("error", (err) => {
              console.log("\u548C\u98CE\u5929\u6C14API\u4EE3\u7406\u9519\u8BEF:", err);
            });
            proxy.on("proxyReq", (proxyReq, req) => {
              console.log("\u548C\u98CE\u5929\u6C14API\u4EE3\u7406\u8BF7\u6C42:", req.method, req.url);
              proxyReq.removeHeader("clientid");
              proxyReq.removeHeader("authorization");
              proxyReq.removeHeader("Authorization");
              proxyReq.removeHeader("Content-Language");
              proxyReq.setHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
              proxyReq.setHeader("Accept", "application/json");
            });
            proxy.on("proxyRes", (proxyRes, req) => {
              console.log("\u548C\u98CE\u5929\u6C14API\u4EE3\u7406\u54CD\u5E94:", proxyRes.statusCode, req.url);
            });
          }
        }
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          // additionalData: '@use "@/assets/styles/variables.module.scss as *";'
          // javascriptEnabled: true
          api: "modern-compiler"
        }
      },
      postcss: {
        plugins: [
          // 浏览器兼容性
          autoprefixer(),
          {
            postcssPlugin: "internal:charset-removal",
            AtRule: {
              charset: (atRule) => {
                atRule.remove();
              }
            }
          }
        ]
      }
    },
    // 预编译
    optimizeDeps: {
      include: [
        "vue",
        "vue-router",
        "pinia",
        "axios",
        "@vueuse/core",
        "echarts",
        "vue-i18n",
        "@vueup/vue-quill",
        "image-conversion",
        "element-plus/es/components/**/css"
      ]
    }
  };
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
