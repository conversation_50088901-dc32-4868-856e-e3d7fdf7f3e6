<template>
    <div class="event-card-wrapper">
        <div class="event-card">
            <div class="title-banner">
                <div class="banner-title">
                    <button class="nav-btn" @click="prevEvent" :disabled="eventList.length === 0" title="上一条事件">&nbsp;</button>
                    <span>事件情况</span>
                    <button class="nav-btn" @click="nextEvent" :disabled="eventList.length === 0" title="下一条事件">&nbsp;</button>
                </div>
                <div class="card-content" v-if="!loading && currentEvent">
                    <div class="info-row">
                        <span class="label">发现时间</span>
                        <span class="value">{{ formatTime(currentEvent.happenTime) }}</span>
                    </div>

                    <div class="info-row">
                        <span class="label">事件类型</span>
                        <span class="value">{{ currentEvent.eventTypeName || '未知类型' }}</span>
                    </div>

                    <div class="info-row">
                        <span class="label">现场描述</span>
                        <span class="value description">{{ currentEvent.liveDescription || '暂无描述' }}</span>
                    </div>

                    <div class="info-row">
                        <span class="label">现场处理</span>
                        <span class="value description">{{ currentEvent.liveDealDescription || '暂无处理信息' }}</span>
                    </div>
                </div>
                <div class="card-content loading-content" v-else-if="loading">
                    <div class="loading-text">加载中...</div>
                </div>
                <div class="card-content no-data-content" v-else>
                    <div class="no-data-text">暂无事件数据</div>
                </div>
            </div>
        </div>
    </div>
</template>


<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { listNoPageEvents } from '@/api/subProject/operation/event/index'
import { EventVO } from '@/api/subProject/operation/event/types'
import { getCategory } from '@/api/common/category/index'

// 扩展的事件接口，包含事件类型名称
interface ProcessedEventVO extends EventVO {
    eventTypeName?: string
}

// 数据管理
const eventList = ref<ProcessedEventVO[]>([]) // 存储前100条事件
const currentIndex = ref(0) // 当前显示的事件索引
const currentEvent = ref<ProcessedEventVO | null>(null) // 当前显示的事件
const loading = ref(false) // 加载状态

// 定时器管理
let autoSwitchTimer: NodeJS.Timeout | null = null // 30秒自动切换
let dataRefreshTimer: NodeJS.Timeout | null = null // 30分钟数据刷新

// 事件类型名称缓存
const eventClassNames = ref<Map<string, string>>(new Map())

// 获取事件类型名称
const getEventClassName = async (eventClass: string): Promise<string> => {
    if (!eventClass) return '未知类型'

    // 检查缓存
    if (eventClassNames.value.has(eventClass)) {
        return eventClassNames.value.get(eventClass)!
    }

    try {
        const response = await getCategory(eventClass)
        const categoryName = response.data?.name || '未知类型'

        // 缓存结果
        eventClassNames.value.set(eventClass, categoryName)
        return categoryName
    } catch (error) {
        console.error('获取事件类型名称失败:', error)
        return '未知类型'
    }
}

// 获取前100条事件数据
const fetchTop100Events = async () => {
    loading.value = true
    try {
        console.log('获取前100条事件数据...')
        const response = await listNoPageEvents({
            pageNum: 1,
            pageSize: 100 // 获取前100条
        })

        const events = response.data || []
        console.log('获取到事件数据:', events.length, '条')

        // 按发生时间倒序排序（最新的在前）
        events.sort((a, b) => {
            const timeA = new Date(a.happenTime || '').getTime()
            const timeB = new Date(b.happenTime || '').getTime()
            return timeB - timeA
        })

        // 处理事件数据，获取事件类型名称
        const processedEvents = await Promise.all(
            events.slice(0, 100).map(async (event) => ({
                ...event,
                eventTypeName: await getEventClassName(event.eventClass)
            }))
        )

        eventList.value = processedEvents
        currentIndex.value = 0
        updateCurrentEvent()
    } catch (error) {
        console.error('获取事件数据失败:', error)
        eventList.value = []
        currentEvent.value = null
    } finally {
        loading.value = false
    }
}

// 更新当前显示的事件
const updateCurrentEvent = () => {
    if (eventList.value.length === 0) {
        currentEvent.value = null
        return
    }

    currentEvent.value = eventList.value[currentIndex.value]
}

// 切换到上一条事件
const prevEvent = () => {
    if (eventList.value.length === 0) return
    currentIndex.value = currentIndex.value > 0 ? currentIndex.value - 1 : eventList.value.length - 1
    updateCurrentEvent()
    //console.log('切换到上一条事件，当前索引:', currentIndex.value)
}

// 切换到下一条事件
const nextEvent = () => {
    if (eventList.value.length === 0) return
    currentIndex.value = (currentIndex.value + 1) % eventList.value.length
    updateCurrentEvent()
    //console.log('切换到下一条事件，当前索引:', currentIndex.value)
}

// 启动自动切换定时器
const startAutoSwitch = () => {
    if (autoSwitchTimer) clearInterval(autoSwitchTimer)
    autoSwitchTimer = setInterval(() => {
        nextEvent()
    }, 30 * 1000) // 30秒
    console.log('已启动30秒自动切换定时器')
}

// 启动数据刷新定时器
const startDataRefresh = () => {
    if (dataRefreshTimer) clearInterval(dataRefreshTimer)
    dataRefreshTimer = setInterval(
        () => {
            console.log('定时刷新事件数据...')
            fetchTop100Events()
        },
        30 * 60 * 1000
    ) // 30分钟
    console.log('已启动30分钟数据刷新定时器')
}

// 时间格式化
const formatTime = (time?: string) => {
    if (!time) return '暂无时间'

    try {
        const date = new Date(time)
        if (isNaN(date.getTime())) {
            return time
        }

        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        const hours = String(date.getHours()).padStart(2, '0')
        const minutes = String(date.getMinutes()).padStart(2, '0')
        const seconds = String(date.getSeconds()).padStart(2, '0')

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    } catch (error) {
        return time
    }
}

onMounted(async () => {
    await fetchTop100Events()
    startAutoSwitch()
    startDataRefresh()
})

onUnmounted(() => {
    if (autoSwitchTimer) {
        clearInterval(autoSwitchTimer)
        autoSwitchTimer = null
    }
    if (dataRefreshTimer) {
        clearInterval(dataRefreshTimer)
        dataRefreshTimer = null
    }
    console.log('已清理所有定时器')
})
</script>

<style lang="scss" scoped>
.event-card-wrapper {
    width: 70%;
    height: 90%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;

    // 左侧装饰图片
    &::before {
        content: '';
        position: absolute;
        left: -35px;
        top: 140px;
        transform: translateY(-50%);
        width: 60px;
        height: 210px;
        background-image: url('@/assets/images/left.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-position: center;
        z-index: 1;
        // background: red;
    }

    // 右侧装饰图片
    &::after {
        content: '';
        position: absolute;
        right: -35px;
        top: 140px;
        transform: translateY(-50%);
        width: 60px;
        height: 210px;
        background-image: url('@/assets/images/right.png');
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
        z-index: 1;
    }
}

.event-card {
    width: 100%;
    height: 100%;
    // background: rgba(0,44,94,0.67);
    // box-shadow: inset 0px -7px 23px 0px rgba(168,209,255,0.13);
    // border-radius: 8px;
    // border: 1px solid rgba(52, 152, 255, 0.3);
    padding: 0;
    color: #fff;
    font-family: 'Microsoft YaHei', sans-serif;
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 2;
}

.title-banner {
    width: 100%;
    height: 100%;
    background-image: url('@/assets/images/Frame.png');
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    flex-shrink: 0;
    // background: red;
}

.banner-title {
    font-size: 20px;
    font-weight: bold;
    color: #fff;
    text-align: center;
    letter-spacing: 1px;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    padding: 10px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    // 切换按钮样式
    .nav-btn {
        background: rgba(255, 255, 255, 0);
        border: 0px solid rgba(255, 255, 255, 0.3);
        color: #fff;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        cursor: pointer;
        font-size: 24px;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;

        // &:hover:not(:disabled) {
        //     background: rgba(255, 255, 255, 0.2);
        //     border-color: rgba(255, 255, 255, 0.5);
        //     transform: scale(1.1);
        // }

        &:disabled {
            opacity: 0.3;
            cursor: not-allowed;
        }
    }
}

// 已移除的样式类 - 不再使用
// .header-icon, .header-decoration, .card-title

.card-content {
    display: flex;
    flex-direction: column;
    gap: 2px;
    flex: 1;
    padding: 0 16px 16px 16px;
    width: 100%;
    max-height: 200px;
    overflow-y: auto;
    overflow-x: hidden;
    &::-webkit-scrollbar {
        width: 8px;
    }

    &::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
        background: rgba(52, 152, 255, 0.6);
        border-radius: 4px;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    &::-webkit-scrollbar-thumb:hover {
        background: rgba(52, 152, 255, 0.8);
    }
}

.info-row {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    padding: 6px 0;
}

.info-row:last-child {
    border-bottom: none;
}

.label {
    font-weight: 500;
    min-width: 64px;
    flex-shrink: 0;
    margin-right: 10px;
}

.value {
    color: #fff;
    font-size: 16px;
    line-height: 1.4;
    flex: 1;
}

.value.description {
    line-height: 1.5;
    text-align: justify;
}

// 加载状态样式
.loading-content {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 120px;

    .loading-text {
        color: rgba(255, 255, 255, 0.8);
        font-size: 16px;
    }
}

// 无数据状态样式
.no-data-content {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 120px;

    .no-data-text {
        color: rgba(255, 255, 255, 0.6);
        font-size: 16px;
    }
}
</style>
