<!-- 按巡检设备查询组件 -->
<template>
    <div>
        <!-- 原有的设备列表 -->
        <div v-show="!showDeviceConfig">
            <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
                <div v-show="showSearch" class="mb-[10px]">
                    <el-card shadow="hover">
                        <el-form ref="queryFormRef" :model="queryParams" :inline="true">
                            <el-form-item label="管理单元" prop="unitId">
                                <el-select v-model="queryParams.unitId" placeholder="请选择管理单元" clearable @change="handleUnitChange">
                                    <el-option v-for="unit in unitOptions" :key="unit.id" :label="unit.name" :value="unit.id" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="房间" prop="roomId">
                                <el-select v-model="queryParams.roomId" placeholder="请选择房间" clearable>
                                    <el-option v-for="room in roomOptions" :key="room.id" :label="room.roomNane" :value="room.id" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="设备分类" prop="categoryPath">
                                <el-tree-select
                                    v-model="selectedQueryCategoryId"
                                    :data="categoryOptions"
                                    :props="{ value: 'id', label: 'name', children: 'children' }"
                                    value-key="id"
                                    placeholder="请选择设备系统分类"
                                    clearable
                                    check-strictly
                                    @change="handleCategoryChange"
                                    style="width: 300px"
                                />
                            </el-form-item>
                            <el-form-item>
                                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                            </el-form-item>
                        </el-form>
                    </el-card>
                </div>
            </transition>
            <el-card shadow="never">
                <template #header>
                    <div class="card-header">
                        <span>巡检设备列表</span>
                        <div>
                            <el-button link type="primary" icon="Search" @click="showSearch = !showSearch">搜索</el-button>
                        </div>
                    </div>
                </template>
                <el-table v-loading="loading" :data="deviceList" @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55" align="center" />
                    <el-table-column label="设备名称" align="center" prop="remark" min-width="120" />
                    <el-table-column label="设备编码" align="center" prop="code" min-width="120" />
                    <el-table-column label="机电分系统" align="center" prop="firstLevelCategory" min-width="120" />
                    <el-table-column label="机电子系统" align="center" prop="secondLevelCategory" min-width="120" />
                    <el-table-column label="设备类型" align="center" prop="thirdLevelCategory" min-width="120" />
                    <el-table-column label="管理单元名称" align="center" prop="unitName" min-width="120" />
                    <el-table-column label="房间名称" align="center" prop="roomName" min-width="120" />
                    <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                        <template #default="scope">
                            <el-tooltip content="查看详情" placement="top">
                                <el-button link type="primary" @click="handleView(scope.row)">巡检项</el-button>
                            </el-tooltip>
                        </template>
                    </el-table-column>
                </el-table>
                <pagination
                    v-show="total > 0"
                    v-model:page="queryParams.pageNum"
                    v-model:limit="queryParams.pageSize"
                    :total="total"
                    @pagination="getList"
                />
            </el-card>
        </div>

        <!-- 设备巡检配置组件 -->
        <DeviceConfigedInspectionItemsComponent
            v-show="showDeviceConfig"
            :device-info="selectedDevice"
            @back="handleBackToDeviceList"
            @view-records="handleViewInspectionRecords"
        />
    </div>
</template>

<script setup name="ByDeviceComponent" lang="ts">
import { ref, onMounted, getCurrentInstance, ComponentInternalInstance } from 'vue'
import { ElMessage } from 'element-plus'
import { listDevice } from '@/api/subProject/inspection/inspectionByDeviceView'
import { InspectionByDeviceViewVO, InspectionByDeviceViewQuery } from '@/api/subProject/inspection/inspectionByDeviceView/types'
import { listProjectManageUnit } from '@/api/project/manageUnit'
import { ManageUnitVO } from '@/api/project/manageUnit/types'
import { listRoom } from '@/api/project/room'
import { RoomVO } from '@/api/project/room/types'
import { listCategory, getCategoryHierarchyNames } from '@/api/common/category'
import { CategoryVO } from '@/api/common/category/types'
import { useAppStore } from '@/store/modules/app'
import DeviceConfigedInspectionItemsComponent from './DeviceConfigedInspectionItemsComponent.vue'

const { proxy } = getCurrentInstance() as ComponentInternalInstance
const appStore = useAppStore()

const deviceList = ref<InspectionByDeviceViewVO[]>([])
const loading = ref(true)
const showSearch = ref(true)
const ids = ref<Array<string | number>>([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)

// 设备分类选择
const selectedQueryCategoryId = ref('')

// 设备配置组件状态
const showDeviceConfig = ref(false)
const selectedDevice = ref<InspectionByDeviceViewVO | null>(null)

// 查询参数
const queryParams = ref<InspectionByDeviceViewQuery>({
    pageNum: 1,
    pageSize: 10,
    projectId: '',
    unitId: undefined,
    roomId: undefined,
    categoryPath: undefined
})

// 选项数据
const unitOptions = ref<ManageUnitVO[]>([])
const roomOptions = ref<RoomVO[]>([])
const categoryOptions = ref<CategoryVO[]>([])

// 查找分类的辅助函数
const findCategoryById = (list: any[], id: string | number): any => {
    for (const item of list) {
        if (item.id === id) return item
        if (item.children) {
            const found = findCategoryById(item.children, id)
            if (found) return found
        }
    }
    return null
}

const queryFormRef = ref()

/** 查询设备列表 */
const getList = async () => {
    loading.value = true
    try {
        // 设置项目ID
        queryParams.value.projectId = appStore.projectContext.selectedProjectId
        queryParams.value.tempTask = ''
        const response = await listDevice(queryParams.value)

        // 处理返回数据
        if (response) {
            let rawDeviceList: InspectionByDeviceViewVO[] = []

            if (response.rows) {
                // 分页数据格式
                rawDeviceList = response.rows
                total.value = response.total
            } else if (Array.isArray(response.rows)) {
                // 数组格式
                rawDeviceList = response.rows
                total.value = response.rows.length
            }

            // 异步处理设备数据，解析分类信息
            if (rawDeviceList.length > 0) {
                const processedDevices = await Promise.all(rawDeviceList.map((device) => processDeviceData(device)))
                deviceList.value = processedDevices
            } else {
                deviceList.value = []
            }
        } else {
            deviceList.value = []
            total.value = 0
        }
    } catch (error) {
        console.error('获取设备列表失败:', error)
        ElMessage.error('获取设备列表失败')
        deviceList.value = []
        total.value = 0
    } finally {
        loading.value = false
    }
}

/** 处理设备数据，解析分类信息 */
const processDeviceData = async (device: InspectionByDeviceViewVO) => {
    let firstLevelCategory = ''
    let secondLevelCategory = ''
    let thirdLevelCategory = ''

    // 如果有三级分类ID，通过API获取层级名称
    if (device.categoryIdThird) {
        try {
            const response = await getCategoryHierarchyNames(device.categoryIdThird)
            if (response && response.data && Array.isArray(response.data)) {
                const hierarchyNames = response.data
                // 层级名称数组：[祖父节点名称, 父节点名称, 当前节点名称]
                if (hierarchyNames.length >= 3) {
                    firstLevelCategory = hierarchyNames[0] || '' // 机电分系统（一级）
                    secondLevelCategory = hierarchyNames[1] || '' // 机电子系统（二级）
                    thirdLevelCategory = hierarchyNames[2] || '' // 设备类型（三级）
                } else if (hierarchyNames.length === 2) {
                    secondLevelCategory = hierarchyNames[0] || ''
                    thirdLevelCategory = hierarchyNames[1] || ''
                } else if (hierarchyNames.length === 1) {
                    thirdLevelCategory = hierarchyNames[0] || ''
                }
            }
        } catch (error) {
            console.warn('获取分类层级名称失败:', error)
            // 降级处理：解析 categoryPath
            if (device.categoryPath) {
                const pathArray = device.categoryPath.split('/')
                if (pathArray.length >= 1) firstLevelCategory = pathArray[0]
                if (pathArray.length >= 2) secondLevelCategory = pathArray[1]
                if (pathArray.length >= 3) thirdLevelCategory = pathArray[2]
            }
        }
    } else if (device.categoryPath) {
        // 降级处理：解析 categoryPath
        const pathArray = device.categoryPath.split('/')
        if (pathArray.length >= 1) firstLevelCategory = pathArray[0]
        if (pathArray.length >= 2) secondLevelCategory = pathArray[1]
        if (pathArray.length >= 3) thirdLevelCategory = pathArray[2]
    }

    return {
        ...device,
        firstLevelCategory,
        secondLevelCategory,
        thirdLevelCategory
    }
}

/** 获取管理单元列表 */
const getUnitList = async () => {
    try {
        const projectId = appStore.projectContext.selectedProjectId
        if (!projectId) return

        const response = await listProjectManageUnit(projectId)
        unitOptions.value = Array.isArray(response.data) ? response.data : []
    } catch (error) {
        console.error('获取管理单元列表失败:', error)
        unitOptions.value = []
    }
}

/** 管理单元变化时获取房间列表 */
const handleUnitChange = async (unitId: string | number) => {
    roomOptions.value = []
    queryParams.value.roomId = undefined

    if (!unitId) return

    try {
        const projectId = appStore.projectContext.selectedProjectId
        const response = await listRoom({
            projectId: projectId,
            unitId: unitId,
            pageNum: 1,
            pageSize: 1000
        })

        // 处理返回数据 - 修复类型错误
        if (response && response.rows) {
            roomOptions.value = Array.isArray(response.rows) ? response.rows : []
        }
    } catch (error) {
        console.error('获取房间列表失败:', error)
        roomOptions.value = []
    }
}

/** 设备分类变化时处理 categoryPath */
const handleCategoryChange = () => {
    if (!selectedQueryCategoryId.value) {
        queryParams.value.categoryPath = undefined
    } else {
        const selectedCategory = findCategoryById(categoryOptions.value, selectedQueryCategoryId.value)
        if (selectedCategory && selectedCategory.path) {
            queryParams.value.categoryPath = selectedCategory.path
        }
    }
}

/** 获取设备分类树 */
const getCategoryTree = async () => {
    try {
        const projectId = appStore.projectContext.selectedProjectId
        if (!projectId) return

        // 获取机电设备分类列表
        const response = await listCategory({
            projectId: projectId,
            kind: 'equipment'
        })

        if (response && response.data) {
            // 使用 handleTree 构建树形结构
            const treeData = proxy?.handleTree<CategoryVO>(response.data, 'id', 'parentId')
            categoryOptions.value = treeData || []
        } else {
            categoryOptions.value = []
        }
    } catch (error) {
        console.error('获取设备分类树失败:', error)
        categoryOptions.value = []
    }
}

/** 搜索按钮操作 */
const handleQuery = () => {
    // 处理分类路径
    if (!selectedQueryCategoryId.value) {
        queryParams.value.categoryPath = undefined
    } else {
        const selectedCategory = findCategoryById(categoryOptions.value, selectedQueryCategoryId.value)
        if (selectedCategory && selectedCategory.path) {
            queryParams.value.categoryPath = selectedCategory.path
        }
    }

    queryParams.value.pageNum = 1

    getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
    queryFormRef.value?.resetFields()
    selectedQueryCategoryId.value = ''
    queryParams.value = {
        pageNum: 1,
        pageSize: 10,
        projectId: appStore.projectContext.selectedProjectId,
        unitId: undefined,
        roomId: undefined,
        categoryPath: undefined
    }
    roomOptions.value = []
    handleQuery()
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: InspectionByDeviceViewVO[]) => {
    ids.value = selection.map((item) => item.id)
    single.value = selection.length !== 1
    multiple.value = !selection.length
}

/** 查看设备巡检配置 */
const handleView = (row: InspectionByDeviceViewVO) => {
    console.log('选中的设备信息:', row)

    // 确保设备信息完整
    if (!row.id) {
        ElMessage.error('设备信息不完整，无法查看配置项')
        return
    }

    selectedDevice.value = row
    showDeviceConfig.value = true
}

/** 返回设备列表 */
const handleBackToDeviceList = () => {
    showDeviceConfig.value = false
    selectedDevice.value = null
}

/** 查看巡检记录 */
const handleViewInspectionRecords = (configItem: any) => {
    // 跳转到巡检记录详情页面或打开弹窗
    ElMessage.info(`查看巡检记录: ${configItem.configName}`)
    console.log('查看巡检记录:', configItem)
}

/** 初始化 */
onMounted(() => {
    getUnitList()
    getCategoryTree()
    getList()
})
</script>

<style scoped>
.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
</style>
