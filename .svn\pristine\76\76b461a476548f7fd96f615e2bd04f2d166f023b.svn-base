<template>
    <div class="p-2 equipment-page">
        <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
            <div v-show="showSearch" class="mb-0">
                <el-card shadow="hover">
                    <el-form ref="queryFormRef" :model="queryParams" :inline="true">
                        <el-form-item label="管理单元" prop="unitId">
                            <el-select v-model="queryParams.unitId" placeholder="请输入管理单元" clearable @keyup.enter="handleQuery">
                                <el-option v-for="(item, key) in manageUntList" :value="item.id" :label="item.name"></el-option>
                            </el-select>
                        </el-form-item>

                        <!-- <el-form-item label="设备类别" prop="categoryIdThird">
                            <el-input v-model="queryParams.categoryIdThird" placeholder="请输入" clearable @keyup.enter="handleQuery" />

                        </el-form-item> -->
                        <el-form-item label="设备分类" prop="categoryIdSecond">
                            <!-- <el-input v-model="queryParams.categoryIdThird" placeholder="请输入设施分类" clearable @keyup.enter="handleQuery" /> -->
                            <el-tree-select
                                clearable
                                @change="handleQuery"
                                v-model="selectedQueryCategoryId"
                                :data="categoryList"
                                :props="{ value: 'id', label: 'name', children: 'children' }"
                                value-key="id"
                                placeholder="请选择父节点编号"
                                check-strictly
                            />
                        </el-form-item>
                        <el-form-item label="设备名称" prop="name">
                            <el-input v-model="queryParams.name" placeholder="请输入设备名称" clearable @keyup.enter="handleQuery" />
                        </el-form-item>
                        <el-form-item label="备注名称" prop="remark">
                            <el-input v-model="queryParams.remark" placeholder="请输入备注名称" clearable @keyup.enter="handleQuery" />
                        </el-form-item>
                        <el-form-item label="设备编码" prop="code">
                            <el-input v-model="queryParams.code" placeholder="请输入" clearable @keyup.enter="handleQuery" />
                        </el-form-item>
                        <!-- <el-form-item label="" prop="categoryIdFirst">
              <el-input v-model="queryParams.categoryIdFirst" placeholder="请输入" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="" prop="categoryIdSecond">
              <el-input v-model="queryParams.categoryIdSecond" placeholder="请输入" clearable @keyup.enter="handleQuery" />
            </el-form-item> -->

                        <el-form-item class="filter-actions">
                            <el-button type="primary" @click="handleQuery">搜索</el-button>
                            <el-button @click="resetQuery">重置</el-button>
                        </el-form-item>
                    </el-form>
                </el-card>
            </div>
        </transition>

        <el-card shadow="never">
            <template #header>
                <el-row :gutter="10" class="mb8 toolbar-actions">
                    <el-col :span="1.5">
                        <el-button class="toolbar-btn btn-add" @click="handleAdd" v-hasPermi="['basic:equipment:add']">新增</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button class="toolbar-btn btn-edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['basic:equipment:edit']"
                            >修改</el-button
                        >
                    </el-col>
                    <el-col :span="1.5">
                        <el-button class="toolbar-btn btn-delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['basic:equipment:remove']"
                            >删除</el-button
                        >
                    </el-col>
                </el-row>
            </template>

            <el-table v-loading="loading" :data="equipmentList" @selection-change="handleSelectionChange" stripe>
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="" align="center" prop="id" v-if="false" />

                <el-table-column label="设备名称" align="center" prop="name" />
                <el-table-column label="备注" align="center" prop="remark" />
                <el-table-column label="管理单元" align="center" prop="unitId">
                    <template #default="scope">
                        {{ manageUntList.find((unit) => unit.id === scope.row.unitId)?.name }}
                    </template>
                </el-table-column>

                <el-table-column label="机电分系统" align="center" prop="categoryIdFirst">
                    <template #default="scope">
                        {{ getFirstLevelCategory(scope.row.categoryIdThird) }}
                    </template>
                </el-table-column>
                <el-table-column label="机电子系统" align="center" prop="categoryIdThird" width="120">
                    <template #default="scope">
                        {{ getSecondLevelCategory(scope.row.categoryIdThird) }}
                    </template>
                </el-table-column>
                <el-table-column label="设备类型" align="center" prop="categoryIdThird">
                    <template #default="scope">
                        {{ getCategoryNameById(scope.row.categoryIdThird) }}
                    </template>
                </el-table-column>
                <!-- <el-table-column label="模型ID" align="center" prop="modleId" /> -->
                <!-- <el-table-column label="品牌" align="center" prop="brand">
                    <template #default="scope">
                        <dict-tag :options="device_brands_new" :value="scope.row.brand" />
                    </template>
                </el-table-column> -->

                <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="260">
                    <template #default="scope">
                        <div class="op-actions">
                            <el-button link type="primary" class="op-link op-info" @click="handleInformation(scope.row)">
                                <img class="op-icon" src="@/assets/images/equipment-icon.png" alt="档案" />
                                设备档案
                            </el-button>
                            <el-button
                                link
                                type="primary"
                                class="op-link op-edit"
                                @click="handleUpdate(scope.row)"
                                v-hasPermi="['basic:equipment:edit']"
                            >
                                <img class="op-icon" src="@/assets/images/edit-icon.png" alt="修改" />
                                修改
                            </el-button>
                            <el-button
                                link
                                type="danger"
                                class="op-link op-delete"
                                @click="handleDelete(scope.row)"
                                v-hasPermi="['basic:equipment:remove']"
                            >
                                <img class="op-icon" src="@/assets/images/delete-icon.png" alt="删除" />
                                删除
                            </el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>

            <pagination
                v-show="total > 0"
                :total="total"
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                @pagination="getList"
            />
        </el-card>
        <!-- 添加或修改设施即设备信息对话框 -->
        <el-dialog :title="dialog.title" v-model="dialog.visible" width="750px" append-to-body>
            <el-form ref="equipmentFormRef" :model="form" :rules="rules" label-width="120px">
                <el-tabs v-model="activeTabName">
                    <el-tab-pane label="属性" name="basic">
                        <el-row :gutter="gutter">
                            <el-col :span="12">
                                <el-form-item label="管理单元" prop="unitId">
                                    <el-select v-model="form.unitId" placeholder="请输入管理单元" @change="getLineTree" clearable>
                                        <el-option v-for="(item, key) in manageUntList" :value="item.id" :label="item.name"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="设备类型" prop="categoryIdThird">
                                    <!-- <el-select v-model="form.categoryIdThird" placeholder="请输入" /> -->
                                    <el-cascader
                                        v-model="form.categoryIdThird"
                                        :options="categoryList"
                                        :props="{ value: 'id', label: 'name', children: 'children', expandTrigger: 'hover', emitPath: false }"
                                    ></el-cascader>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="gutter">
                            <el-col :span="12">
                                <el-form-item label="设备名称" prop="name">
                                    <el-input v-model="form.name" placeholder="请输入" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="设备编码" prop="code">
                                    <el-input v-model="form.code" placeholder="请输入" />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="gutter">
                            <el-col :span="12">
                                <el-form-item label="编码类型" prop="codeType">
                                    <el-select v-model="form.codeType" placeholder="请选择编码类型">
                                        <el-option v-for="dict in codeTypeOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-row :gutter="gutter"> </el-row>
                            <el-col :span="12">
                                <el-form-item label="备注" prop="remark">
                                    <el-input v-model="form.remark" placeholder="请输入备注" />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="gutter" v-if="form.codeType == 'management_number'">
                            <el-col :span="12">
                                <el-form-item label="起范围" prop="arrrangeBgn">
                                    <el-input v-model="form.arrangeBgn" placeholder="请输入编码" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="止范围" prop="arrrangeEnd">
                                    <el-input v-model="form.arrangeEnd" placeholder="请输入编码" />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="gutter">
                            <el-col :span="12">
                                <el-form-item label="时钟范围起" prop="timeArrangeBgn">
                                    <el-input v-model="form.timeArrangeBgn" placeholder="请输入时钟范围-bgn，字典项：时钟范围" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="时钟范围止" prop="timeArrangeEnd">
                                    <el-input v-model="form.timeArrangeEnd" placeholder="请输入时钟范围-end，字典项：时钟范围" />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="gutter" v-if="form.codeType != 'room_code'">
                            <el-col :span="12">
                                <el-form-item label="起始里程" prop="bgnKilometer">
                                    <el-input v-model="form.bgnKilometer" placeholder="请输入起始里程" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="终止里程" prop="endKilometer">
                                    <el-input v-model="form.endKilometer" placeholder="请输入终止里程" />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="gutter" v-if="form.codeType == 'room_code'">
                            <el-col :span="12">
                                <el-form-item label="房间编码" prop="roomId">
                                    <el-select v-model="form.roomId" placeholder="请选择房间编码" clearable>
                                        <el-option v-for="room in rooms" :key="room.id" :label="room.roomNane" :value="room.id" />
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="gutter">
                            <el-col :span="12">
                                <el-form-item label="设备归属单位" prop="owner">
                                    <el-select v-model="form.owner" placeholder="请选择设备归属单位">
                                        <el-option v-for="dict in device_owner" :key="dict.value" :label="dict.label" :value="dict.value" />
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="模型ID" prop="modelId">
                                    <el-input v-model="form.modelId" placeholder="请输入" />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="gutter">
                            <el-col :span="12">
                                <el-form-item label="安装时间" prop="installDate2">
                                    <el-date-picker clearable v-model="form.installDate" type="date" placeholder="请选择安装时间"> </el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="品牌" prop="brand">
                                    <el-select v-model="form.brand" placeholder="请输入品牌">
                                        <el-option v-for="dict in device_brands_new" :key="dict.value" :label="dict.label" :value="dict.value" />
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="gutter">
                            <el-col :span="12">
                                <el-form-item label="型号" prop="specification">
                                    <el-input v-model="form.specification" placeholder="请输入型号" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12"> </el-col>
                        </el-row>
                        <el-row :gutter="gutter">
                            <el-col :span="24">
                                <el-form-item label="车道" prop="roadwayId">
                                    <el-cascader
                                        v-model="form.roadwayId"
                                        :options="lineTree"
                                        multiple
                                        collapse-tags
                                        collapse-tags-tooltip
                                        :props="{
                                            value: 'id',
                                            label: 'name',
                                            children: 'children',
                                            expandTrigger: 'hover',
                                            emitPath: false
                                        }"
                                        placeholder="请选择车道"
                                    ></el-cascader>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-tab-pane>
                    <el-tab-pane label="图片" name="images">
                        <el-form-item prop="images">
                            <image-upload></image-upload>
                        </el-form-item>
                    </el-tab-pane>
                    <el-tab-pane label="文件" name="files">
                        <file-upload></file-upload>
                    </el-tab-pane>
                </el-tabs>

                <!-- <el-form-item label="自动生成的序列号" prop="seq">
                    <el-input v-model="form.seq" placeholder="请输入自动生成的序列号" />
                </el-form-item> -->
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="Equipment" lang="ts">
import { useAppStore } from '@/store/modules/app'
import { listEquipment, getEquipment, delEquipment, addEquipment, updateEquipment } from '@/api/subProject/basic/equipment'
import { EquipmentVO, EquipmentQuery, EquipmentForm } from '@/api/subProject/basic/equipment/types'
import { listProjectManageUnit } from '@/api/project/manageUnit'
import { ManageUnitVO } from '@/api/project/manageUnit/types'
import { getProject } from '@/api/project/project'
import { CategoryVO, CategoryQuery } from '@/api/common/category/types'
import { listCategory } from '@/api/common/category'
import { listLineToRoadwayTreeByUnitId } from '@/api/project/line'
import { TunnelTreeNode } from '@/api/types'
import { listCodeTypesOfProject } from '@/api/project/projectCode'
import { useRouter } from 'vue-router'
import { listDevice } from '@/api/subProject/basic/device'
import { DeviceVO } from '@/api/subProject/basic/device/types'
import { listRoom } from '@/api/project/room'
import type { RoomVO } from '@/api/project/room/types'
import { watchEffect } from 'vue'

const { proxy } = getCurrentInstance() as ComponentInternalInstance
const appStore = useAppStore()
const router = useRouter()
const { code_type, device_owner, device_brands_new } = toRefs<any>(proxy?.useDict('code_type', 'device_owner', 'device_brands_new'))

const selectedQueryCategoryId = ref('')
const equipmentList = ref<DeviceVO[]>([])
const buttonLoading = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref<Array<string | number>>([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const activeTabName = ref('basic')
const queryFormRef = ref<ElFormInstance>()
const equipmentFormRef = ref<ElFormInstance>()
const gutter = ref(10)
const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
})

const initFormData: EquipmentForm = {
    id: undefined,
    unitId: undefined,
    name: undefined,
    code: undefined,
    seq: undefined,
    roadwayId: undefined,
    codeType: undefined,
    timeArrangeBgn: undefined,
    timeArrangeEnd: undefined,
    bgnKilometer: undefined,
    equipmentOwner: undefined,
    endKilometer: undefined,
    modelId: undefined,
    remark: undefined,
    owner: undefined,
    installDate: undefined,
    brand: undefined,
    categoryIdFirst: undefined,
    categoryIdSecond: undefined,
    categoryIdThird: undefined,
    specification: undefined,
    images: undefined,
    files: undefined,
    projectId: undefined
}
const data = reactive<PageData<EquipmentForm, EquipmentQuery>>({
    form: { ...initFormData },
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectId: undefined,
        unitId: undefined,
        name: undefined,
        code: undefined,
        remark: undefined,
        kind: 'equipment',
        configedInspection: '',
        categoryPath: undefined,
        categoryIdFirst: undefined,
        categoryIdSecond: undefined,
        categoryIdThird: undefined,
        params: {}
    },
    rules: {
        unitId: [{ required: true, message: '请选择管理单元', trigger: 'blur' }],
        categoryIdThird: [{ required: true, message: '请选择设备类型', trigger: 'blur' }],
        remark: [{ required: true, message: '请填写备注信息', trigger: 'blur' }],
        codeType: [{ required: true, message: '请选择编码类型', trigger: 'blur' }],
        bgnCode: [{ required: true, message: '请填写起范围编码', trigger: 'blur' }],
        bgnKilometer: [{ required: true, message: '请填写起始里程', trigger: 'blur' }],
        owner: [{ required: true, message: '请选择设备归属单位', trigger: 'blur' }],
        installDate: [{ required: true, message: '请输入安装时间', trigger: 'blur' }],
        roomId: [{ required: true, message: '请选择房间编码', trigger: 'change' }]
    }
})

const { queryParams, form, rules } = toRefs(data)
const currentProjectId = ref('')
const manageUntList = ref<ManageUnitVO[]>([])
const categoryList = ref<CategoryVO[]>([])
const lineTree = ref<TunnelTreeNode[]>([])
/** 查询设施即设备信息列表 */
const getList = async () => {
    loading.value = true
    // 确保在调用接口前设置项目ID
    if (!queryParams.value.projectId) {
        queryParams.value.projectId = appStore.projectContext.selectedProjectId
    }
    queryParams.value.kind = 'equipment'
    const res = await listDevice(queryParams.value)
    equipmentList.value = res.rows
    total.value = res.total
    loading.value = false
}

/** 取消按钮 */
const cancel = () => {
    reset()
    dialog.visible = false
}

/** 表单重置 */
const reset = () => {
    form.value = { ...initFormData }
    equipmentFormRef.value?.resetFields()
}

/** 搜索按钮操作 */
const handleQuery = () => {
    if (!selectedQueryCategoryId.value) {
        queryParams.value.categoryPath = ''
    } else {
        const selectFacatity = findCategoryById(categoryList.value, selectedQueryCategoryId.value)
        queryParams.value.categoryPath = selectFacatity.path
        console.log('selectFacatity', selectFacatity.path)
    }
    queryParams.value.pageNum = 1
    getList()
}
function findCategoryById(list, id) {
    for (const item of list) {
        if (item.id === id) return item
        if (item.children) {
            const found = findCategoryById(item.children, id)
            if (found) return found
        }
    }
    return null
}

/** 重置按钮操作 */
const resetQuery = () => {
    queryFormRef.value?.resetFields()
    handleQuery()
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: EquipmentVO[]) => {
    ids.value = selection.map((item) => item.id)
    single.value = selection.length != 1
    multiple.value = !selection.length
}

/** 新增按钮操作 */
const handleAdd = () => {
    reset()
    dialog.visible = true
    dialog.title = '添加设备信息'
}

/** 修改按钮操作 */
const handleUpdate = async (row?: EquipmentVO) => {
    reset()
    const _id = row?.id || ids.value[0]
    const res = await getEquipment(_id)
    Object.assign(form.value, res.data)
    await getLineTree(form.value.unitId)
    dialog.visible = true
    dialog.title = '修改设备信息'
}

/** 提交按钮 */
const submitForm = () => {
    equipmentFormRef.value?.validate(async (valid: boolean) => {
        if (valid) {
            buttonLoading.value = true
            currentProjectId.value = appStore.projectContext.selectedProjectId
            form.value.projectId = currentProjectId.value

            // 修复 roadwayId 处理逻辑
            if (form.value.roadwayId) {
                // 确保 roadwayId 是数组才调用 join
                if (Array.isArray(form.value.roadwayId)) {
                    form.value.roadwayId = form.value.roadwayId.join(',')
                } else if (typeof form.value.roadwayId === 'string') {
                    // 如果已经是字符串，保持不变
                    // form.value.roadwayId = form.value.roadwayId;
                } else {
                    // 其他类型转为字符串
                    form.value.roadwayId = String(form.value.roadwayId)
                }
            }

            if (form.value.id) {
                await updateEquipment(form.value).finally(() => (buttonLoading.value = false))
            } else {
                await addEquipment(form.value).finally(() => (buttonLoading.value = false))
            }
            proxy?.$modal.msgSuccess('操作成功')
            dialog.visible = false
            await getList()
        }
    })
}

/** 删除按钮操作 */
const handleDelete = async (row?: EquipmentVO) => {
    const _ids = row?.id || ids.value
    await proxy?.$modal.confirm('是否确认删除设施即设备信息编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false))
    await delEquipment(_ids)
    proxy?.$modal.msgSuccess('删除成功')
    await getList()
}

/** 设备档案按钮操作 */
const handleInformation = (row: EquipmentVO) => {
    router.push('equipmentInformation?id=' + row.id)
}

/** 导出按钮操作 */
const handleExport = () => {
    proxy?.download(
        'basic/equipment/export',
        {
            ...queryParams.value
        },
        `equipment_${new Date().getTime()}.xlsx`
    )
}
const getManageUnitList = async () => {
    currentProjectId.value = appStore.projectContext.selectedProjectId
    manageUntList.value = (await listProjectManageUnit(currentProjectId.value)).data
}
/** 查询category列表 */
const getEquipmentCategoryList = async () => {
    loading.value = true
    queryParams.value.projectId = appStore.projectContext.selectedProjectId

    const res = await listCategory(queryParams.value)

    const data = proxy?.handleTree<CategoryVO>(res.data, 'id', 'parentId')
    console.log('treeData', data)
    if (data) {
        categoryList.value = data
        loading.value = false
    }
}
const getFirstLevelCategory = (categoryId: string) => {
    if (!categoryId || !categoryList.value) return ''

    for (const firstLevel of categoryList.value) {
        for (const secondLevel of firstLevel.children || []) {
            for (const thirdLevel of secondLevel.children || []) {
                if (thirdLevel.id === categoryId) {
                    return firstLevel.name
                }
            }
        }
    }
    return ''
}

// 获取第二级分类名称
const getSecondLevelCategory = (categoryId: string) => {
    if (!categoryId || !categoryList.value) return ''

    for (const firstLevel of categoryList.value) {
        for (const secondLevel of firstLevel.children || []) {
            for (const thirdLevel of secondLevel.children || []) {
                if (thirdLevel.id === categoryId) {
                    return secondLevel.name
                }
            }
        }
    }
    return ''
}

const getCategoryNameById = (id: string) => {
    if (!id) return ''
    const findCategory = (list: any[], targetId: string): string => {
        for (const item of list) {
            if (item.id === targetId) {
                return item.name
            }
            if (item.children && item.children.length) {
                const found = findCategory(item.children, targetId)
                if (found) return found
            }
        }
        return ''
    }
    return findCategory(categoryList.value, id)
}
// const getCodeTypeOptions = async () => {
//     currentProjectId.value = appStore.projectContext.selectedProjectId
//     // 获取项目下的所有codeTypeOptio
//     const resp = await listCodeTypesOfProject(currentProjectId.value);
//     const codeTypesArrUnderProject = resp.data.map((it) => it.codeType);
//     codeTypeOptions.value = code_type.value.filter((item) => codeTypesArrUnderProject.includes(item.value));
// };
const getLineTree = async (unitId: string) => {
    lineTree.value = (await listLineToRoadwayTreeByUnitId(unitId)).data
}
const codeTypeOptions = ref([
    {
        label: '桩号',
        value: 'management_number'
    },
    {
        label: '里程号',
        value: 'mileage_number'
    },
    {
        label: '房间编码',
        value: 'room_code'
    }
])

// 房间列表数据
const rooms = ref<RoomVO[]>([])

// 监听管理单元变化
watchEffect(async () => {
    if (form.value.unitId) {
        await getRooms(form.value.unitId)
    } else {
        rooms.value = []
        form.value.roomId = undefined
    }
})

// 获取房间列表
const getRooms = async (unitId: string | number) => {
    try {
        const res = await listRoom({
            projectId: appStore.projectContext.selectedProjectId,
            unitId: unitId,
            pageNum: 1,
            pageSize: 1000 // 获取足够多的房间数据
        })
        // 处理返回的数据结构
        // const data = res.data as any;

        rooms.value = res?.rows || []
    } catch (error) {
        console.error('获取房间列表失败:', error)
        proxy?.$modal.msgError('获取房间列表失败')
        rooms.value = []
    }
}

onMounted(() => {
    // 首先设置项目ID
    queryParams.value.projectId = appStore.projectContext.selectedProjectId

    getManageUnitList()
    getEquipmentCategoryList()
    getList()
})
</script>
<style lang="scss" scoped>
.equipment-page {
    :deep(.el-card) {
        background: transparent !important;
        border: none !important;
        box-shadow: none !important;
    }

    :deep(.el-card__header) {
        border-bottom: none !important;
        padding-top: 0 !important;
    }

    :deep(.el-card__body) {
        background: transparent !important;
        padding-bottom: 0 !important;
    }

    /* 输入/下拉统一皮肤 */
    :deep(.el-input__wrapper),
    :deep(.el-select__wrapper) {
        background: #232d45 !important;
        border-radius: 6px !important;
        box-shadow: none !important;
        min-height: 36px;
        height: 36px;
        padding: 5px 10px;
    }

    :deep(.el-input__inner::placeholder),
    :deep(.el-select__placeholder) {
        color: #8291a9 !important;
        opacity: 1;
    }

    :deep(.el-input__inner),
    :deep(.el-select .el-select__selected-item > span:not(.el-select__placeholder)) {
        color: #ffffff !important;
    }

    :deep(.el-select__wrapper.is-focused) {
        box-shadow: none !important;
    }

    /* 管理单元：禁用文本光标 */
    :deep(.el-form-item[label='管理单元'] .el-select__wrapper),
    :deep(.el-form-item[label='管理单元'] .el-select) {
        cursor: default !important;
    }

    :deep(.el-form-item[label='管理单元'] .el-select .el-select__selected-item > span) {
        cursor: default !important;
    }

    :deep(.el-form-item[label='管理单元'] .el-input__inner) {
        caret-color: transparent !important;
    }

    /* 搜索/重置按钮 */
    :deep(.filter-actions .el-button:not(.is-link)) {
        border-radius: 6px !important;
        height: 40px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        font-size: 14px !important;
    }

    :deep(.filter-actions .el-button.el-button--primary:not(.is-link)) {
        background-color: #4286f3 !important;
        border-color: #4286f3 !important;
        color: #ffffff !important;
    }

    :deep(.filter-actions .el-button.el-button--primary:not(.is-link))::before {
        content: '';
        display: inline-block;
        width: 16px;
        height: 16px;
        background: url('@/assets/images/search-icon.png') no-repeat center/contain;
        margin-right: 6px;
    }

    :deep(.filter-actions .el-button:not(.el-button--primary):not(.is-link)) {
        background-color: #808892 !important;
        border-color: #808892 !important;
        color: #ffffff !important;
    }

    :deep(.filter-actions .el-button:not(.el-button--primary):not(.is-link))::before {
        content: '';
        display: inline-block;
        width: 16px;
        height: 16px;
        background: url('@/assets/images/restart-icon.png') no-repeat center/contain;
        margin-right: 6px;
    }

    /* 表格透明化与去边框 */
    :deep(.el-table) {
        --el-table-bg-color: transparent;
        --el-table-header-bg-color: transparent;
        --el-table-tr-bg-color: transparent;
        --el-table-border-color: rgba(255, 255, 255, 0.08);
        background-color: transparent !important;
        color: #ffffff;
    }

    :deep(.el-table__inner-wrapper::before),
    :deep(.el-table__inner-wrapper::after),
    :deep(.el-table::before),
    :deep(.el-table--border::after),
    :deep(.el-table__border-left-patch) {
        display: none !important;
        background: transparent !important;
    }

    :deep(.el-table__header th) {
        background-color: transparent !important;
        border-bottom: none !important;
        height: 44px;
        text-align: center;
    }

    :deep(.el-table__header th .cell) {
        color: #aed7f2 !important;
        font-size: 14px !important;
        font-weight: 500 !important;
        text-align: center;
        position: relative;
        top: -3px;
    }

    :deep(.el-table td) {
        text-align: center !important;
        height: 48px;
        background-color: transparent !important;
    }

    :deep(.el-table .cell) {
        color: #ffffff !important;
        font-size: 13px !important;
        line-height: 1.4;
        padding: 8px 12px;
    }

    :deep(.el-table__body tr) {
        background: transparent !important;
    }

    :deep(.el-table--striped .el-table__body tr.el-table__row--striped) {
        background: linear-gradient(to right, rgba(79, 158, 249, 0.02) 0%, rgba(79, 158, 249, 0.12) 60%, rgba(79, 158, 249, 0.22) 100%) !important;
    }

    :deep(.el-table__body tr:hover > td) {
        background-color: rgba(66, 134, 243, 0.08) !important;
    }

    /* 表头整行背景（对齐全寿命首页） */
    :deep(.el-table__header-wrapper) {
        position: relative;
    }

    :deep(.el-table__header-wrapper)::before {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        height: 44px;
        background: rgb(31, 43, 78);
        border-radius: 6px;
        pointer-events: none;
        z-index: 0;
    }

    :deep(.el-table thead),
    :deep(.el-table th.el-table__cell) {
        position: relative;
        z-index: 1;
    }

    /* 表体每行间隔线 */
    :deep(.el-table__body tr > td) {
        border-bottom: 1px solid rgba(255, 255, 255, 0.06) !important;
    }

    /* 顶部工具按钮 */
    .toolbar-actions {
        align-items: center;
    }

    :deep(.toolbar-btn) {
        border: none !important;
        color: #ffffff !important;
        font-size: 14px !important;
        height: 40px;
        padding: 0 16px 0 42px;
        border-radius: 8px;
        position: relative;
    }

    :deep(.toolbar-btn::before) {
        content: '';
        position: absolute;
        left: 14px;
        top: 50%;
        transform: translateY(-50%);
        width: 14px;
        height: 14px;
        background-repeat: no-repeat;
        background-size: contain;
    }

    :deep(.btn-add) {
        background-color: #2a59c4 !important;
    }

    :deep(.btn-add::before) {
        background-image: url('@/assets/images/add-icon.png');
    }

    :deep(.btn-edit) {
        background-color: #2ba1a0 !important;
    }

    :deep(.btn-edit::before) {
        background-image: url('@/assets/images/edit-white-icon.png');
    }

    :deep(.btn-delete) {
        background-color: #921121 !important;
    }

    :deep(.btn-delete::before) {
        background-image: url('@/assets/images/delete-white-icon.png');
    }

    /* 操作列图标式按钮 */
    .op-actions {
        display: flex;
        align-items: center;
        gap: 8px;
        justify-content: center;
        padding-right: 8px;
    }

    .op-link {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        padding: 0 6px;
    }

    .op-icon {
        width: 14px;
        height: 14px;
        display: inline-block;
        margin-right: 4px;
    }

    .op-info {
        color: #4286f3 !important;
    }

    .op-edit {
        color: #42f3e9 !important;
    }

    .op-delete {
        color: #d62121 !important;
    }
}
</style>
