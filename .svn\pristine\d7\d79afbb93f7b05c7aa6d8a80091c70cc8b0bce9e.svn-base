export interface TaskVO {
    /**
     *
     */
    id: string | number

    /**
     * 项目-id
     */
    projectId: string | number

    /**
     * 任务父-id
     */
    parentId: string | number

    /**
     * 计划任务名称
     */
    name: string

    /**
     * 任务类型：巡检、维养、封道
     */
    taskType: string

    /**
     * 任务定义-id
     */
    defineId: string | number

    /**
     * 月度任务-id
     */
    weekTaskId: string | number

    /**
     * 所属年度
     */
    year: number

    /**
     * 月份
     */
    month: number

    /**
     * 第n周
     */
    week: number

    /**
     * 日任务类型-字典项（默认白班）：白班、晚班
     */
    shiftType: string

    /**
     * 作业日期
     */
    taskDate: string

    /**
     * 任务阶段：plan，task
     */
    taskStep: string

    /**
     * 开始时间
     */
    bgnDate: string

    /**
     * 结束时间
     */
    endDate: string

    /**
     * 班组-id
     */
    teamId: string | number

    /**
     * 排序
     */
    sort: number

    /**
     * 周发布状态：published，unpub
     */
    publishState: string

    /**
     * 专业类型，字典项：tnl_speciality
     */
    speciality: string

    /**
     * 维养内容，字典项：maintenance_content
     */
    maintenanceContent: string

    /**
     * 临时任务：yes，no
     */
    tempTask: string

    /**
     * 临时任务的资源id，关联tnl_task_resource表
     */
    tempResourceId: string
    /**
     * 当前任务状态，指当前的业务环节，字典项：play_task_status
     */
    currentStatus?: string

    /**
     * 是否可拆分：0-否，1-是
     */
    splitable: number
}

export interface TaskForm extends BaseEntity {
    /**
     *
     */
    id?: string | number

    /**
     * 项目-id
     */
    projectId?: string | number

    /**
     * 任务父-id
     */
    parentId?: string | number

    /**
     * 计划任务名称
     */
    name?: string

    /**
     * 任务类型：巡检、维养、封道
     */
    taskType?: string

    /**
     * 任务定义-id
     */
    defineId?: string | number

    /**
     * 月度任务-id
     */
    weekTaskId?: string | number

    /**
     * 所属年度
     */
    year?: number

    /**
     * 月份
     */
    month?: number

    /**
     * 第n周
     */
    week?: number

    /**
     * 日任务类型-字典项（默认白班）：白班、晚班
     */
    shiftType?: string

    /**
     * 作业日期
     */
    taskDate?: string

    /**
     * 任务阶段：plan，task
     */
    taskStep?: string

    /**
     * 开始时间
     */
    bgnDate?: string

    /**
     * 结束时间
     */
    endDate?: string

    /**
     * 作业开始时间
     */
    taskStartDate?: string

    /**
     * 作业结束时间
     */
    taskFinishDate?: string

    /**
     * 作业结束时间
    /**
     * 班组-id
     */
    teamId?: string | number

    /**
     * 排序
     */
    sort?: number

    /**
     * 周发布状态：published，unpub
     */
    publishState?: string

    /**
     * 工作流审批状态，字典项：wf_business_status
     */
    status?: string

    /**
     * 当前任务状态，指当前的业务环节，字典项：play_task_status
     */
    currentStatus?: string

    /**
     * 临时任务：yes，no
     */
    tempTask: string

    /**
     * 临时任务的资源id，关联tnl_task_resource表
     */
    tempResourceId: string
}

export interface TaskQuery extends PageQuery {
    /**
     * 项目-id
     */
    projectId?: string | number

    /**
     * 任务父-id
     */
    parentId?: string | number

    /**
     * 计划任务名称
     */
    name?: string

    /**
     * 任务类型：巡检、维养、封道
     */
    taskType?: string
    taskTypeSub?: string

    /**
     * 任务定义-id
     */
    defineId?: string | number

    /**
     * 月度任务-id
     */
    weekTaskId?: string | number

    /**
     * 所属年度
     */
    year?: number

    /**
     * 月份
     */
    month?: number

    /**
     * 第n周
     */
    week?: number

    /**
     * 日任务类型-字典项（默认白班）：白班、晚班
     */
    shiftType?: string

    /**
     * 作业日期
     */
    taskDate?: string

    /**
     * 任务阶段：plan，task
     */
    taskStep?: string

    /**
     * 开始时间
     */
    bgnDate?: string

    /**
     * 结束时间
     */
    endDate?: string

    /**
     * 班组-id
     */
    teamId?: string | number

    /**
     * 排序
     */
    sort?: number

    /**
     * 周发布状态：published，unpub
     */
    publishState?: string

    /**
     * 临时任务：yes，no
     */
    tempTask: string
    currentStatus: string

    /**
     * 临时任务的资源id，关联tnl_task_resource表
     */
    tempResourceId: string

    /**
     * 日期范围参数
     */
    params?: any
}

export interface TaskWeekViewVO {
    /**
     * 主键ID
     */
    id: string | number

    /**
     * 项目ID
     */
    projectId: string | number

    /**
     * 任务名称
     */
    name: string

    /**
     * 任务类型：巡检、维养、封道
     */
    taskType: string

    /**
     * 所属年度
     */
    year: number

    /**
     * 月份
     */
    month: number

    /**
     * 第n周
     */
    week: number

    /**
     * 作业日期
     */
    taskDate: string

    /**
     * 任务定义ID
     */
    defineId: string | number

    /**
     * 日任务类型-字典项（默认白班）：白班、晚班
     */
    shiftType: string

    /**
     * 排序
     */
    sort: number

    /**
     * 专业类型，字典项：tnl_speciality
     */
    speciality: string

    /**
     * 频次类型
     */
    frequencyType: string

    /**
     * 频次
     */
    frequency: number

    /**
     * 维养内容，字典项：maintenance_content
     */
    maintenanceContent: string

    /**
     * 周发布状态：published，unpub
     */
    publishState: string

    /**
     * 当前任务状态，指当前的业务环节，字典项：play_task_status
     */
    currentStatus: string
}

export interface QueryDayPlanByDeviceBo extends PageQuery {
    /**
     * 项目ID
     */
    projectId: string

    /**
     * 设备ID
     */
    deviceId: string

    /**
     * 计划类型（可选）
     */
    planType?: string

    /**
     * 维修养护内容（可选）
     */
    maintenanceContent?: string

    /**
     * 频次单位（可选）
     */
    frequencyUnit?: string

    /**
     * 频次（可选）
     */
    frequency?: string

    /**
     * 起始日期（可选）
     */
    startDate?: string

    /**
     * 结束日期（可选）
     */
    endDate?: string

    /**
     * 查询参数（包含日期范围等）
     */
    params?: any
}

export interface QueryDeviceInspectionQuery extends PageQuery {
    /**
     * 项目ID
     */
    projectId: string

    /**
     * 设备ID
     */
    deviceId: string

    /**
     * 开始日期（可选）
     */
    startDate?: string

    /**
     * 结束日期（可选）
     */
    endDate?: string

    /**
     * 任务步骤（可选）
     */
    taskStep?: string

    /**
     * 任务类型（可选）
     */
    taskType?: string

    /**
     * 查询参数（包含日期范围等）
     */
    params?: any
}

export interface NextAssigneeForm {
    /**
     * 待办ID
     */
    toDoId?: string | number

    /**
     * 意见
     */
    opinion?: string

    /**
     * 表单数据
     */
    formData?: string

    /**
     * 工作流的操作类型：
     * APPROVE - 审批
     * ROLLBACK - 退回
     * SUSPENDED - 挂起
     * REVOKE - 撤回
     */
    wfOperation?: string

    /**
     * 下一环节处理人
     */
    nextAssignees?: number[]

    /**
     * 🆕 撤回目标环节代码（撤回功能）
     * 当wfOperation为REVOKE且revokeType为TO_SPECIFIC时使用
     */
    targetActivityCode?: string

    /**
     * 🆕 撤回类型（撤回功能）
     * TO_START - 撤回到起始状态
     * TO_PREVIOUS - 撤回到上一环节
     * TO_SPECIFIC - 撤回到指定环节
     */
    revokeType?: string

    /**
     * 🆕 分支条件（分支功能）
     * 用于工作流分支判断的条件表达式
     * 例如：priority=HIGH, status=URGENT等
     */
    branchCondition?: string

    /**
     * 🆕 是否启用分支模式（分支功能，默认false）
     * true - 启用分支模式，根据branchCondition进行分支判断
     * false - 使用传统线性流转
     */
    enableBranch?: boolean

    /**
     * 🆕 业务数据（分支功能）
     * 用于分支条件判断的业务数据对象
     */
    businessData?: any
}

// /**
//  * 巡检任务视图对象
//  */
// export interface InspectionTaskVO extends TaskVO {
//   /**
//    * 巡检路线ID列表
//    */
//   inspectionLineIdList?: string[];

//   /**
//    * 巡检路线名称标签（格式化显示用）
//    */
//   inspectionLineNameLabel?: string;
// }

// /**
//  * 维养详情VO
//  * 对应Java类 MaintainDetailVo
//  */
// export interface MaintainDetailVO {
//     /** 计划名称 */
//     title?: string;
//     /** 任务子类型 */
//     taskSubType?: string;
//     /** 专业类型 */
//     speciality?: string;
//     /** 管理单元 */
//     unitNameLabel?: string;
//     /** 养护内容 */
//     maintainContent?: string;
//     /** 养护项目名称 */
//     maintainItemNames?: string;
//     /** 计划日期 */
//     planDate?: string;
//     /** 计划开始时间 */
//     planBeginTime?: string;
//     /** 计划结束时间 */
//     planEndTime?: string;
//     /** 频率 */
//     frequency?: string;
//     /** 频率单位 */
//     frequencyUnit?: string;
// }

/**
 * 任务资源项业务对象
 */
export interface TaskResourceItemBo {
    /**
     * 主键ID
     */
    id?: string

    /**
     * 项目ID
     */
    projectId?: string

    /**
     * 任务定义ID
     */
    defineId?: string

    /**
     * 任务ID
     */
    taskId?: string

    /**
     * 巡检线路ID
     */
    inspectionLineId?: string

    /**
     * 管理单元ID
     */
    unitId?: string

    /**
     * 设施设备分类ID
     */
    deviceCategoryId?: string

    /**
     * 设备ID
     */
    deviceId?: string

    /**
     * 道路ID
     */
    roadId?: string

    /**
     * 道路名称
     */
    roadName?: string

    /**
     * 起始桩号
     */
    startStake?: string

    /**
     * 结束桩号
     */
    endStake?: string

    /**
     * 作业项目ID列表
     */
    jobIdList?: string

    /**
     * 数据类型
     */
    dataType?: string

    /**
     * 删除标志
     */
    delFlag?: number
}

/**
 * 任务拆分请求对象
 */
export interface TaskSplitRequest {
    /**
     * 拆分项列表，每个SplitItem代表一个拆分任务
     */
    splitItems: SplitItem[]
}

/**
 * 拆分项，包含一组资源组合
 */
export interface SplitItem {
    /**
     * 资源项列表，使用TaskResourceItemBo包含完整的资源信息
     */
    splitItem: TaskResourceItemBo[]
}

/**
 * 任务拆分结果对象
 */
export interface TaskSplitResult {
    /**
     * 原任务ID
     */
    originalTaskId: string

    /**
     * 新创建的任务ID列表
     */
    newTaskIds: string[]

    /**
     * 拆分数量
     */
    splitCount: number

    /**
     * 操作时间
     */
    operationTime: string

    /**
     * 操作结果消息
     */
    message: string
}
