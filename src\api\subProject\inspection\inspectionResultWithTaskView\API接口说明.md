# InspectionResultWithTaskView API 接口说明

## 📋 概述

基于服务端 `InspectionResultWithTaskViewVo.java` 和 `InspectionResultWithTaskViewBo.java` 创建的前端 API 接口，参考 `inspectionByDeviceView` 的结构和模式。

## 🗂️ 文件结构

```
df-vue-plus-ui/src/api/subProject/inspection/inspectionResultWithTaskView/
├── index.ts          # API 接口方法
├── types.ts          # TypeScript 类型定义
└── API接口说明.md    # 接口说明文档
```

## 📊 数据类型定义

### **InspectionResultWithTaskViewVO**
```typescript
export interface InspectionResultWithTaskViewVO {
    id: string | number              // 主键ID
    deviceId: string | number        // 设备ID
    lineId: string | number          // 线路ID
    lineName: string                 // 线路名称/路线名称
    itemId: string | number          // 巡检项ID
    valueString: string              // 字符串值
    valueBool: string                // 布尔值
    valueData: string                // 数据值
    taskName: string                 // 任务名称
    bgnDate: string                  // 开始日期
    taskStartDate: string            // 任务开始日期
    taskFinishDate: string           // 任务完成日期
    shiftType: string                // 班次类型
}
```

### **InspectionResultWithTaskViewForm**
```typescript
export interface InspectionResultWithTaskViewForm extends BaseEntity {
    // 所有字段都是可选的，用于表单提交
    id?: string | number
    deviceId?: string | number
    lineId?: string | number
    lineName?: string
    itemId?: string | number
    valueString?: string
    valueBool?: string
    valueData?: string
    taskName?: string
    bgnDate?: string
    taskStartDate?: string
    taskFinishDate?: string
    shiftType?: string
}
```

### **InspectionResultWithTaskViewQuery**
```typescript
export interface InspectionResultWithTaskViewQuery extends PageQuery {
    deviceId?: string | number       // 设备ID筛选
    lineId?: string | number         // 线路ID筛选
    lineName?: string                // 线路名称筛选
    itemId?: string | number         // 巡检项ID筛选
    taskName?: string                // 任务名称筛选
    shiftType?: string               // 班次类型筛选
    bgnDateRange?: string[]          // 开始日期范围
    taskStartDateRange?: string[]    // 任务开始日期范围
    taskFinishDateRange?: string[]   // 任务完成日期范围
    params?: any                     // 其他参数
}
```

## 🔧 API 接口方法

### **1. 基础 CRUD 操作**

#### **查询列表**
```typescript
listInspectionResultWithTaskView(query?: InspectionResultWithTaskViewQuery): AxiosPromise<TableDataInfo<InspectionResultWithTaskViewVO>>
```
- **URL**: `/inspection/inspectionResultWithTaskView/list`
- **方法**: `GET`
- **用途**: 分页查询巡检结果与任务视图列表

#### **查询详情**
```typescript
getInspectionResultWithTaskView(id: string | number): AxiosPromise<InspectionResultWithTaskViewVO>
```
- **URL**: `/inspection/inspectionResultWithTaskView/{id}`
- **方法**: `GET`
- **用途**: 根据ID查询单条记录详情

#### **新增记录**
```typescript
addInspectionResultWithTaskView(data: InspectionResultWithTaskViewForm): AxiosPromise<any>
```
- **URL**: `/inspection/inspectionResultWithTaskView`
- **方法**: `POST`
- **用途**: 新增巡检结果与任务视图记录

#### **修改记录**
```typescript
updateInspectionResultWithTaskView(data: InspectionResultWithTaskViewForm): AxiosPromise<any>
```
- **URL**: `/inspection/inspectionResultWithTaskView`
- **方法**: `PUT`
- **用途**: 修改巡检结果与任务视图记录

#### **删除记录**
```typescript
delInspectionResultWithTaskView(id: string | number | Array<string | number>): AxiosPromise<any>
```
- **URL**: `/inspection/inspectionResultWithTaskView/{id}`
- **方法**: `DELETE`
- **用途**: 删除单条或多条记录

### **2. 扩展查询方法**

#### **按设备查询**
```typescript
listInspectionResultByDevice(deviceId: string | number, query?: InspectionResultWithTaskViewQuery): AxiosPromise<InspectionResultWithTaskViewVO[]>
```
- **URL**: `/inspection/inspectionResultWithTaskView/device/{deviceId}`
- **方法**: `GET`
- **用途**: 根据设备ID查询相关的巡检结果

#### **按线路查询**
```typescript
listInspectionResultByLine(lineId: string | number, query?: InspectionResultWithTaskViewQuery): AxiosPromise<InspectionResultWithTaskViewVO[]>
```
- **URL**: `/inspection/inspectionResultWithTaskView/line/{lineId}`
- **方法**: `GET`
- **用途**: 根据线路ID查询相关的巡检结果

#### **按任务查询**
```typescript
listInspectionResultByTask(taskName: string, query?: InspectionResultWithTaskViewQuery): AxiosPromise<InspectionResultWithTaskViewVO[]>
```
- **URL**: `/inspection/inspectionResultWithTaskView/task/{taskName}`
- **方法**: `GET`
- **用途**: 根据任务名称查询相关的巡检结果

### **3. 批量操作方法**

#### **批量查询**
```typescript
batchGetInspectionResultWithTaskView(ids: Array<string | number>): AxiosPromise<InspectionResultWithTaskViewVO[]>
```
- **URL**: `/inspection/inspectionResultWithTaskView/batch`
- **方法**: `POST`
- **用途**: 根据ID数组批量查询记录

#### **导出数据**
```typescript
exportInspectionResultWithTaskView(query?: InspectionResultWithTaskViewQuery): AxiosPromise<any>
```
- **URL**: `/inspection/inspectionResultWithTaskView/export`
- **方法**: `POST`
- **用途**: 导出巡检结果与任务视图数据

#### **统计数据**
```typescript
statisticsInspectionResultWithTaskView(query?: InspectionResultWithTaskViewQuery): AxiosPromise<any>
```
- **URL**: `/inspection/inspectionResultWithTaskView/statistics`
- **方法**: `GET`
- **用途**: 统计巡检结果与任务视图数据

## 🎯 使用示例

### **1. 在组件中导入**
```typescript
import { 
    listInspectionResultWithTaskView,
    getInspectionResultWithTaskView,
    addInspectionResultWithTaskView,
    updateInspectionResultWithTaskView,
    delInspectionResultWithTaskView,
    listInspectionResultByDevice
} from '@/api/subProject/inspection/inspectionResultWithTaskView'

import { 
    InspectionResultWithTaskViewVO,
    InspectionResultWithTaskViewQuery,
    InspectionResultWithTaskViewForm
} from '@/api/subProject/inspection/inspectionResultWithTaskView/types'
```

### **2. 查询列表示例**
```typescript
const queryParams = reactive<InspectionResultWithTaskViewQuery>({
    pageNum: 1,
    pageSize: 10,
    deviceId: '123',
    taskName: '日常巡检',
    shiftType: 'day'
})

const getList = async () => {
    try {
        const response = await listInspectionResultWithTaskView(queryParams)
        if (response.data) {
            dataList.value = response.data.rows
            total.value = response.data.total
        }
    } catch (error) {
        console.error('查询失败:', error)
    }
}
```

### **3. 按设备查询示例**
```typescript
const getDeviceInspectionResults = async (deviceId: string | number) => {
    try {
        const response = await listInspectionResultByDevice(deviceId, {
            taskStartDateRange: ['2024-01-01', '2024-12-31']
        })
        if (response.data) {
            deviceResults.value = response.data
        }
    } catch (error) {
        console.error('查询设备巡检结果失败:', error)
    }
}
```

### **4. 新增记录示例**
```typescript
const addRecord = async (formData: InspectionResultWithTaskViewForm) => {
    try {
        await addInspectionResultWithTaskView(formData)
        ElMessage.success('新增成功')
        getList() // 刷新列表
    } catch (error) {
        console.error('新增失败:', error)
        ElMessage.error('新增失败')
    }
}
```

## 🔄 与服务端对应关系

### **前端类型 ↔ 服务端类型**
- `InspectionResultWithTaskViewVO` ↔ `InspectionResultWithTaskViewVo.java`
- `InspectionResultWithTaskViewForm` ↔ `InspectionResultWithTaskViewBo.java`
- `InspectionResultWithTaskViewQuery` ↔ 查询参数封装

### **字段映射**
| 前端字段 | 服务端字段 | 说明 |
|---------|-----------|------|
| `id` | `id` | 主键ID |
| `deviceId` | `deviceId` | 设备ID |
| `lineId` | `lineId` | 线路ID |
| `lineName` | `lineName` | 线路名称 |
| `itemId` | `itemId` | 巡检项ID |
| `valueString` | `valueString` | 字符串值 |
| `valueBool` | `valueBool` | 布尔值 |
| `valueData` | `valueData` | 数据值 |
| `taskName` | `taskName` | 任务名称 |
| `bgnDate` | `bgnDate` | 开始日期 |
| `taskStartDate` | `taskStartDate` | 任务开始日期 |
| `taskFinishDate` | `taskFinishDate` | 任务完成日期 |
| `shiftType` | `shiftType` | 班次类型 |

## 🚀 特性优势

1. **完整的 CRUD 操作**: 提供增删改查的完整接口
2. **多维度查询**: 支持按设备、线路、任务等多种方式查询
3. **批量操作**: 支持批量查询和删除操作
4. **数据导出**: 支持数据导出功能
5. **统计分析**: 提供数据统计接口
6. **类型安全**: 完整的 TypeScript 类型定义
7. **扩展性强**: 易于添加新的查询方法和功能

现在您可以在前端项目中使用这些 API 接口来操作巡检结果与任务视图数据！
