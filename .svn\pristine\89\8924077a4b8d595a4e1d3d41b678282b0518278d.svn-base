<!-- 巡检作业列表 -->
<template>
    <div class="p-2">
        <el-radio-group v-model="tabPosition" style="margin-bottom: 10px">
            <el-radio-button value="task">按巡检作业</el-radio-button>
            <el-radio-button value="equip">按巡检设备</el-radio-button>
        </el-radio-group>
        <!-- 按巡检作业 -->
        <div v-if="tabPosition === 'task'">
            <ByInspectionTaskComponent />
        </div>
        <!-- 按巡检设备 -->
        <div v-if="tabPosition === 'equip'">
            <ByDeviceComponent></ByDeviceComponent>
        </div>
    </div>
</template>

<script setup lang="ts">
import ByInspectionTaskComponent from './ByInspectionTaskComponent.vue'
import ByDeviceComponent from './ByDeviceComponent.vue'
import {
    listYearPlanCatalog,
    getYearPlanCatalog,
    delYearPlanCatalog,
    addYearPlanCatalog,
    updateYearPlanCatalog
} from '@/api/subProject/plan/yearPlanCatalog'
import { YearPlanCatalogVO, YearPlanCatalogQuery, YearPlanCatalogForm } from '@/api/subProject/plan/yearPlanCatalog/types'
import TaskListSel from '../../components/TaskListSel.vue'
import { useAppStore } from '@/store/modules/app'

const appStore = useAppStore()
const { proxy } = getCurrentInstance() as ComponentInternalInstance

const yearPlanCatalogList = ref<YearPlanCatalogVO[]>([])
const buttonLoading = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref<Array<string | number>>([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const tabPosition = ref('task')

const queryFormRef = ref<ElFormInstance>()
const yearPlanCatalogFormRef = ref<ElFormInstance>()

const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
})

const initFormData: YearPlanCatalogForm = {
    id: undefined,
    projectId: undefined,
    year: undefined,
    name: undefined
}
const data = reactive<PageData<YearPlanCatalogForm, YearPlanCatalogQuery>>({
    form: { ...initFormData },
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectId: undefined,
        name: undefined,
        params: {}
    },
    rules: {
        id: [{ required: true, message: '不能为空', trigger: 'blur' }]
    }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询年度计划目录列表 */
const getList = async () => {
    loading.value = true

    // 确保项目ID已设置
    if (!queryParams.value.projectId) {
        queryParams.value.projectId = appStore.projectContext.selectedProjectId
    }

    // 设置项目ID
    queryParams.value.projectId = appStore.projectContext.selectedProjectId

    const res = await listYearPlanCatalog(queryParams.value)
    yearPlanCatalogList.value = res.rows
    total.value = res.total
    loading.value = false
}

/** 取消按钮 */
const cancel = () => {
    reset()
    dialog.visible = false
}

/** 表单重置 */
const reset = () => {
    form.value = { ...initFormData }
    yearPlanCatalogFormRef.value?.resetFields()
}

/** 搜索按钮操作 */
const handleQuery = () => {
    queryParams.value.pageNum = 1
    queryParams.value.projectId = appStore.projectContext.selectedProjectId
    getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
    queryFormRef.value?.resetFields()
    handleQuery()
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: YearPlanCatalogVO[]) => {
    ids.value = selection.map((item) => item.id)
    single.value = selection.length != 1
    multiple.value = !selection.length
}

/** 新增按钮操作 */
const handleAdd = () => {
    reset()
    dialog.visible = true
    dialog.title = '添加年度计划目录'
}

/** 导航到查看、指派页面 */
const handleAssign = (operationName: string) => {
    // reset();
    // dialog.visible = true;
    // dialog.title = '添加年度计划目录';
    proxy?.$router.push('assign')
}

/** 审批按钮操作 */
const handleConfirm = (operationName: string) => {
    //reset();
    dialog.visible = true
    dialog.title = operationName
    //proxy?.$router.push('assign');
}

/** 修改按钮操作 */
const handleUpdate = async (row?: YearPlanCatalogVO) => {
    reset()
    const _id = row?.id || ids.value[0]
    const res = await getYearPlanCatalog(_id)
    Object.assign(form.value, res.data)
    dialog.visible = true
    dialog.title = '修改年度计划目录'
}

/** 提交按钮 */
const submitForm = () => {
    yearPlanCatalogFormRef.value?.validate(async (valid: boolean) => {
        if (valid) {
            buttonLoading.value = true
            if (form.value.id) {
                await updateYearPlanCatalog(form.value).finally(() => (buttonLoading.value = false))
            } else {
                await addYearPlanCatalog(form.value).finally(() => (buttonLoading.value = false))
            }
            proxy?.$modal.msgSuccess('操作成功')
            dialog.visible = false
            await getList()
        }
    })
}

/** 删除按钮操作 */
const handleDelete = async (row?: YearPlanCatalogVO) => {
    const _ids = row?.id || ids.value
    await proxy?.$modal.confirm('是否确认删除年度计划目录编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false))
    await delYearPlanCatalog(_ids)
    proxy?.$modal.msgSuccess('删除成功')
    await getList()
}

/** 导出按钮操作 */
const handleExport = () => {
    proxy?.download(
        'plan/yearPlanCatalog/export',
        {
            ...queryParams.value
        },
        `yearPlanCatalog_${new Date().getTime()}.xlsx`
    )
}

onMounted(() => {
    queryParams.value.projectId = appStore.projectContext.selectedProjectId
    getList()
})
</script>

<style lang="scss" scoped>
.btn-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .filter {
        flex: 1;
    }
    .export {
        margin-left: auto;
    }
}
</style>
