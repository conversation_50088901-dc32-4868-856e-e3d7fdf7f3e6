<template>
    <el-drawer
        v-model="drawerVisible"
        title="巡检历史记录"
        direction="rtl"
        size="70%"
        :before-close="handleClose"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
    >
        <!-- 抽屉头部信息 -->
        <template #header>
            <div class="drawer-header">
                <h3>巡检历史记录</h3>
                <div class="header-info">
                    <el-tag type="primary">{{ deviceInfo?.remark || deviceInfo?.name }}</el-tag>
                    <el-tag type="success">{{ configItem?.configName }}</el-tag>
                </div>
            </div>
        </template>

        <!-- 抽屉内容 -->
        <div class="drawer-content">
            <!-- 设备和巡检项信息卡片 -->
            <el-card shadow="never" class="mb-4">
                <div class="info-grid">
                    <div class="info-item">
                        <span class="label">设备名称:</span>
                        <span class="value">{{ deviceInfo?.remark || deviceInfo?.name }}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">设备编码:</span>
                        <span class="value">{{ deviceInfo?.code }}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">巡检项:</span>
                        <span class="value">{{ configItem?.configName }}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">巡检类型:</span>
                        <span class="value">{{ getInspectTypeLabel(configItem?.inspectType) }}</span>
                    </div>
                </div>
            </el-card>

            <!-- 查询条件 -->
            <el-card shadow="hover" class="mb-4">
                <el-form ref="queryFormRef" :model="queryParams" :inline="true">
                    <el-form-item label="巡检时间" prop="bgnDateRange">
                        <el-date-picker
                            v-model="queryParams.bgnDateRange"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD"
                            @change="handleQuery"
                            style="width: 300px"
                        />
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-card>

            <!-- 历史记录表格 -->
            <el-card shadow="never">
                <template #header>
                    <div class="card-header">
                        <span class="card-title">历史记录列表</span>
                        <span class="record-count">共 {{ total }} 条记录</span>
                    </div>
                </template>

                <el-table :data="historyList" v-loading="loading" stripe>
                    <el-table-column type="index" label="序号" width="60" align="center" />

                    <!-- 巡检结果 -->
                    <el-table-column label="巡检结果" align="center" min-width="120">
                        <template #default="scope">
                            <el-tag :type="getResultTagType(scope.row)">
                                {{ getInspectionResult(scope.row) }}
                            </el-tag>
                        </template>
                    </el-table-column>

                    <!-- 作业单名称 -->
                    <el-table-column label="作业单名称" align="center" prop="taskName" min-width="150" show-overflow-tooltip />

                    <!-- 巡检时间 -->
                    <el-table-column label="巡检时间" align="center" width="180">
                        <template #default="scope">
                            {{ formatDateTime(scope.row.bgnDate) }}
                        </template>
                    </el-table-column>

                    <!-- 巡检路线 -->
                    <el-table-column label="巡检路线" align="center" prop="lineName" min-width="120" show-overflow-tooltip />

                    <!-- 计划作业日期 -->
                    <el-table-column label="计划作业日期" align="center" width="120">
                        <template #default="scope">
                            {{ formatDate(scope.row.taskStartDate) }}
                        </template>
                    </el-table-column>

                    <!-- 班次 -->
                    <el-table-column label="班次" align="center" prop="shiftType" width="80">
                        <template #default="scope">
                            <el-tag size="small" :type="getShiftTagType(scope.row.shiftType)">
                                {{ getShiftLabel(scope.row.shiftType) }}
                            </el-tag>
                        </template>
                    </el-table-column>

                    <el-table-column label="操作" align="center" width="100">
                        <template #default="scope">
                            <el-button link type="primary" @click="handleViewDetail(scope.row)"> 详情 </el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 分页组件 -->
                <pagination
                    v-show="total > 0"
                    :total="total"
                    v-model:page="queryParams.pageNum"
                    v-model:limit="queryParams.pageSize"
                    @pagination="getList"
                />
            </el-card>
        </div>
    </el-drawer>
</template>

<script setup name="InspectionHistory" lang="ts">
import { ref, reactive, computed, watch, getCurrentInstance, ComponentInternalInstance } from 'vue'
import { ElMessage, FormInstance } from 'element-plus'
import dayjs from 'dayjs'
import { listInspectionResultWithTaskView } from '@/api/subProject/inspection/inspectionResultWithTaskView'
import { InspectionResultWithTaskViewVO, InspectionResultWithTaskViewQuery } from '@/api/subProject/inspection/inspectionResultWithTaskView/types'
import { InspectionByDeviceViewVO } from '@/api/subProject/inspection/inspectionByDeviceView/types'
import { InspectionConfigItemViewVO } from '@/api/subProject/inspection/inspectionConfigItem/types'

const { proxy } = getCurrentInstance() as ComponentInternalInstance

// 获取数据字典
const { inspect_type } = proxy?.useDict('inspect_type') || { inspect_type: ref([]) }

// 定义 props
interface Props {
    visible: boolean // 抽屉显示状态
    deviceInfo: InspectionByDeviceViewVO // 设备信息
    configItem: InspectionConfigItemViewVO // 巡检配置项信息
}

const props = defineProps<Props>()

// 定义 emits
const emit = defineEmits(['update:visible', 'close'])

// 响应式数据
const historyList = ref<InspectionResultWithTaskViewVO[]>([])
const loading = ref(false)
const total = ref(0)

const queryFormRef = ref<FormInstance>()

// 抽屉显示状态
const drawerVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
})

// 查询参数
const queryParams = reactive<InspectionResultWithTaskViewQuery>({
    pageNum: 1,
    pageSize: 10,
    deviceId: undefined,
    itemId: undefined,
    bgnDateRange: undefined,
    params: {}
})

/** 获取巡检类型标签 */
const getInspectTypeLabel = (value?: string) => {
    if (!value) return '-'
    const item = inspect_type.value?.find((item: any) => item.value === value)
    return item ? item.label : value
}

/** 获取巡检结果显示文本 */
const getInspectionResult = (row: InspectionResultWithTaskViewVO) => {
    // 根据数据类型返回相应的结果
    if (row.valueString) {
        return row.valueString
    } else if (row.valueBool) {
        return row.valueBool === 'true' ? '正常' : '异常'
    } else if (row.valueData) {
        return row.valueData
    }
    return '-'
}

/** 获取结果标签类型 */
const getResultTagType = (row: InspectionResultWithTaskViewVO) => {
    const result = getInspectionResult(row)
    if (result === '正常' || result === '合格') {
        return 'success'
    } else if (result === '异常' || result === '不合格') {
        return 'danger'
    } else {
        return 'info'
    }
}

/** 获取班次标签 */
const getShiftLabel = (shiftType?: string) => {
    if (!shiftType) return '-'
    const shiftMap: Record<string, string> = {
        'day': '白班',
        'night': '夜班',
        'morning': '早班',
        'afternoon': '午班',
        'evening': '晚班'
    }
    return shiftMap[shiftType] || shiftType
}

/** 获取班次标签类型 */
const getShiftTagType = (shiftType?: string) => {
    if (!shiftType) return 'info'
    const typeMap: Record<string, string> = {
        'day': 'primary',
        'night': 'info',
        'morning': 'success',
        'afternoon': 'warning',
        'evening': 'danger'
    }
    return typeMap[shiftType] || 'info'
}

/** 格式化日期时间 */
const formatDateTime = (dateTime?: string) => {
    if (!dateTime) return '-'
    return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss')
}

/** 格式化日期 */
const formatDate = (date?: string) => {
    if (!date) return '-'
    return dayjs(date).format('YYYY-MM-DD')
}

/** 查询巡检历史记录列表 */
const getList = async () => {
    // 验证必要参数
    if (!props.deviceInfo?.id || !props.configItem?.id) {
        console.warn('设备信息或巡检项信息不完整')
        return
    }

    loading.value = true
    try {
        // 设置查询参数
        queryParams.deviceId = props.deviceInfo.id
        queryParams.itemId = props.configItem.id

        // 处理时间范围
        if (queryParams.bgnDateRange && queryParams.bgnDateRange.length === 2) {
            queryParams.params = {
                ...queryParams.params,
                beginTime: queryParams.bgnDateRange[0],
                endTime: queryParams.bgnDateRange[1]
            }
        } else {
            // 清除时间参数
            if (queryParams.params) {
                delete queryParams.params.beginTime
                delete queryParams.params.endTime
            }
        }

        console.log('查询历史记录参数:', queryParams)

        // 调用现有的 API 接口
        const response = await listInspectionResultWithTaskView(queryParams)
        console.log('历史记录响应:', response)

        if (response && response.rows) {
            // 处理返回数据
            if (Array.isArray(response.rows)) {
                // 如果直接返回数组
                historyList.value = response.rows
                total.value = response.total
            } else {
                historyList.value = []
                total.value = 0
            }
        } else {
            historyList.value = []
            total.value = 0
        }
    } catch (error) {
        console.error('获取巡检历史记录失败:', error)
        const errorMessage = error instanceof Error ? error.message : '未知错误'
        ElMessage.error(`获取巡检历史记录失败: ${errorMessage}`)
        historyList.value = []
        total.value = 0
    } finally {
        loading.value = false
    }
}

/** 搜索按钮操作 */
const handleQuery = () => {
    queryParams.pageNum = 1
    getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
    queryFormRef.value?.resetFields()
    queryParams.bgnDateRange = undefined
    queryParams.pageNum = 1
    handleQuery()
}

/** 查看详情 */
const handleViewDetail = (row: InspectionResultWithTaskViewVO) => {
    ElMessage.info(`查看详情: ${row.taskName}`)
    console.log('查看详情:', row)
    // 这里可以实现详情查看逻辑，比如打开详情对话框
}

/** 关闭抽屉 */
const handleClose = () => {
    emit('close')
    emit('update:visible', false)
}

/** 监听抽屉显示状态 */
watch(
    () => props.visible,
    (newVisible) => {
        if (newVisible && props.deviceInfo && props.configItem) {
            console.log('抽屉打开，设备信息:', props.deviceInfo)
            console.log('巡检项信息:', props.configItem)
            // 重置查询参数
            queryParams.pageNum = 1
            queryParams.bgnDateRange = undefined
            // 获取历史记录
            getList()
        }
    },
    { immediate: true }
)
</script>

<style scoped>
.drawer-header {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.drawer-header h3 {
    margin: 0;
    color: #303133;
}

.header-info {
    display: flex;
    gap: 8px;
}

.drawer-content {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.info-item {
    display: flex;
    align-items: center;
}

.info-item .label {
    font-weight: 600;
    color: #606266;
    margin-right: 8px;
    min-width: 80px;
}

.info-item .value {
    color: #303133;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
}

.record-count {
    color: #909399;
    font-size: 14px;
}

.mb-4 {
    margin-bottom: 16px;
}
</style>
